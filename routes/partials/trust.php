<?php

use App\Http\Controllers\TrusteeController;
use App\Http\Controllers\BeneficiaryController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\TrustController;

Route::post('add', [TrustController::class, 'create'])->name('trust.add');
Route::get('', [TrustController::class, 'portfolio']);
Route::get('portfolio', [TrustController::class, 'portfolio']);
Route::get('{trustId}', [TrustController::class, 'show']);
Route::put('{trustId}', [TrustController::class, 'update'])->name('trust.update');
Route::patch('{trustId}', [TrustController::class, 'update'])->name('trust.update');
Route::delete('{trustId}', [TrustController::class, 'destroy'])->name('trust.destroy');

Route::post('{trustId}/trustee/add', [TrusteeController::class, 'create'])->name('trustee.add');
Route::get('{trustId}/trustees', [TrusteeController::class, 'getTrustees'])->name('trust.trustees');
Route::get('{trustId}/trustees/{trusteeId}', [TrusteeController::class, 'show'])->name('trustee.show');
Route::put('{trustId}/trustees/{trusteeId}', [TrusteeController::class, 'update'])->name('trustee.update');
Route::patch('{trustId}/trustees/{trusteeId}', [TrusteeController::class, 'update'])->name('trustee.update');
Route::delete('{trustId}/trustees/{trusteeId}', [TrusteeController::class, 'destroy'])->name('trustee.destroy');

Route::post('{trustId}/beneficiary/add', [BeneficiaryController::class, 'create'])->name('beneficiary.add');
Route::get('{trustId}/beneficiaries', [BeneficiaryController::class, 'getBeneficiaries'])->name('trust.beneficiaries');
Route::get('{trustId}/beneficiaries/{beneficiaryId}', [BeneficiaryController::class, 'show'])->name('beneficiary.show');
Route::put('{trustId}/beneficiaries/{beneficiaryId}', [BeneficiaryController::class, 'update'])->name('beneficiary.update');
Route::patch('{trustId}/beneficiaries/{beneficiaryId}', [BeneficiaryController::class, 'update'])->name('beneficiary.update');
Route::delete('{trustId}/beneficiaries/{beneficiaryId}', [BeneficiaryController::class, 'destroy'])->name('beneficiary.destroy');

Route::get('{trustId}/history', [TrustController::class, 'history'])->name('trust.history');
