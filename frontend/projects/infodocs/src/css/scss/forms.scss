@use './variables' as *;
@use 'sass:color';

//
// Forms
// --------------------------------------------------
.subheading {
	font-family: 'Open Sans';
	font-size: 14px;
	font-weight: 700;
	color: $light-blue;
	margin: 10px 0;
}

.btn-orange {
	background-color: $orange;
	border-color: $orange;

	&:hover {
		background-color: $light-blue;
		border-color: $light-blue;
	}

	&:disabled {
		background-color: $gray-light;
		border-color: $gray-light;
	}
	&.promo-code-button {
		height: 32px;
		border-radius: 0 4px 4px 0;
		background-color: #f26d21;
		border: 1px solid #f26d21;

		&:disabled {
			background-color: $gray-light;
			border-color: $gray-light;
		}
	}
}

.btn-blue {
	background-color: $light-blue;
	border-color: $light-blue;

	&:hover {
		// color: $navy;
		background-color: $orange;
		border-color: $orange;
	}

	&:disabled {
		background-color: $gray-light;
		border-color: $gray-light;
	}
}

.alert {
	a {
		text-decoration: underline;
	}
}

// public pages
.col-lg-6 {
	.modal-footer {
		text-align: center;
		border-top: none;
		padding: 0;
		color: red;

		&.skip {
			.btn-nav.btn-prev {
				margin-left: 0px;
			}
		}
	}

	.btn-review {
		width: 255px !important;
		margin-right: 25px;
		margin-left: 25px;
		margin-top: 12px;
	}

	.btn-return {
		background-color: $orange;
		border-color: $orange;
	}

	.btn-nav {
		padding: 6px 0px;
		display: inline-block;
		vertical-align: text-top;

		&.btn-prev {
			margin-left: -31px;
		}
	}
}

.form-group {
	&.half-block {
		width: 50%;
		display: inline-block;
	}

	.control-label {
		&.full-name {
			width: 100%;
		}
	}

	.form-control {
		margin-bottom: 5px;

		&.first-name {
			display: inline-block;
			margin-right: 1%;
			width: 49.5%;
		}

		&.second-name {
			display: inline-block;
			width: 49.5%;
		}

		&.passport-number,
		&.country-of-origin {
			width: 49%;
			margin: 0 1% 0 0;
			display: inline-block;
			vertical-align: top;
		}

		&.gender,
		&.postal-code {
			display: inline-block;
			width: 40%;
		}

		&.city,
		&.race {
			display: inline-block;
			width: 59%;
			margin-right: 1%;
		}

		&.date-time {
			display: inline-block;
			margin-bottom: 0;
			margin-right: 1%;
			width: auto;
		}

		&.date-time:nth-of-type(3) {
			margin-right: 0;
		}
	}

	&.radio-group {
		margin-bottom: 10px;

		&.control-label {
			margin-left: 6px;
		}
	}

	&.auto-checkbox {
		margin-top: 10px;
	}

	&.note-group {
		margin-bottom: 20px;

		label {
			margin-bottom: 0 !important;
		}
	}

	&.resolution-group {
		margin-top: 20px;
		margin-bottom: 24px;
	}

	.date-control {
		width: 33%;
	}

	.placeholder {
		option {
			color: $gray-dark;
		}
	}

	.label-text {
		vertical-align: text-top;
		cursor: pointer;
		color: $gray-light;
		font-size: 12px;
		font-weight: 400;
		margin-bottom: 10px;

		&:hover {
			color: $gray-dark;
		}
	}

	a.forgot-password {
		cursor: pointer;
		color: $gray-light;
		font-size: 12px;
		float: right;
		margin-bottom: 10px;

		&:hover {
			color: $gray-dark;
		}
	}

	#firstName {
		display: inline-block;
		width: 49%;
		margin-right: 1%;
	}

	#secondName {
		display: inline-block;
		width: 49%;
		margin-right: 1%;
	}

	#passportNumber {
		width: 49%;
		margin-right: 1%;
		display: inline-block;
		vertical-align: top;
	}

	#gender,
	#postalCode,
	#posPostalCode,
	#resPostalCode {
		display: inline-block;
		width: 40%;
	}

	#city,
	#posCity,
	#race,
	#resCity {
		display: inline-block;
		width: 59%;
		margin-right: 1%;
	}

	#dateTime {
		display: inline-block;
		margin-bottom: 0;
		width: 16%;
	}

	.date-time {
		display: inline-block;
		margin-bottom: 0;
		margin-right: 1%;
		width: 16%;
	}

	#certificateNo,
	.certificate-no {
		width: 16%;
	}

	#printResolution {
		line-height: 24px !important;
		margin-bottom: 0;
		width: 100%;
	}
}

.form-group.required .control-label::after {
	content: '*';
	color: $orange;
}

.form-group.not-required .control-label::after {
	content: '';
}

.align-right {
	text-align: right;
	.required {
		color: $orange;
		font-weight: bold;
	}
}

.bold-message {
	font-weight: bold;
}

.officer-edit-modal {
	.input-group {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
	}
}

.price-total {
	display: inline-block;
	margin-bottom: 15px;
	width: 49.5%;
	margin-right: 1%;
}

.price-per-share {
	margin-bottom: 15px;
	display: inline-block;
	width: 49.5%;
}

.price-total,
.price-per-share {
	.control-label {
		line-height: 32px;
		margin-bottom: 6px;

		.input-group {
			input {
				margin-bottom: 0px;
			}
		}
	}
}

.vat-number {
	display: inline-block;
	margin-bottom: 10px;
	margin-right: 1%;
	width: 40%;
}

.vat-category {
	display: inline-block;
	margin-bottom: 10px;
	width: 59%;
}

.certificate-group {
	display: inline-block;
	margin-bottom: 15px;
	width: 33.5%;

	.control-label {
		line-height: 32px;
		margin-bottom: 6px;
	}

	#certificateNo,
	.certificate-no {
		width: 46%;
	}
}

.checkbox-option {
	width: 120px;
	margin: 0;
	height: 24px;
	border: 1px solid $input-border;
	color: $gray-dark;
	background-color: $white;
	display: inline-block;
	margin-left: 2px;
}

.checkbox-label {
	line-height: 0 !important;
	font-weight: normal;
	cursor: pointer;

	input[type='checkbox'] {
		margin-right: 5px;
	}

	&.print-resolution {
		line-height: 24px !important;
		margin-bottom: 0;
		width: 100%;
	}
}

.radio-label {
	margin-left: 0 !important;
	cursor: pointer;

	input[type='radio'] {
		margin-right: 5px;
	}

	&.control-label-lite,
	&.radio-label-lite {
		margin-left: 6px;
		margin-right: 10px;
		font-weight: 400;

		img {
			-webkit-filter: grayscale(100%);
			filter: grayscale(100%);
		}
	}
}

.billing-table {
	width: 100%;
	margin-bottom: 15px;

	td {
		padding: 7.5px 2px;
		line-height: 1.5;

		&.billing-amount {
			text-align: right;
			text-wrap: nowrap;
		}
	}
}

.issue-date-verification {
	display: block !important;
	text-align: right !important;

	&.valid {
		color: $brand-success;
	}

	&.invalid {
		color: $brand-danger;
	}
}

// shared forms (reuse modals)
.shared-form {
	.modal-header {
		display: none;
	}

	.modal-body {
		padding: 0;

		.form-group {
			p {
				font-size: 14px;
			}
		}

		p.form-group {
			font-size: 14px;
		}

		.modal-footer {
			border: none;
			text-align: center;
			padding-top: 30px;

			.btn {
				text-align: center;
				width: 160px;
			}

			.btn-nav.btn-prev {
				margin-left: -71px;
				padding: 6px 20px;
				vertical-align: middle;
			}

			img.img.cipc-logo {
				margin-bottom: 0;
			}
		}
	}
}

.complete-signup,
.first-time,
.import-company,
.personal-details,
.confirmation,
.billing-client,
.inviteClient,
.inviteUser,
.support {
	background-color: $white;
	margin: 50px auto !important;
	padding-left: 50px;
	padding-right: 50px;
	padding-top: 50px;
	padding-bottom: 60px;

	h2 {
		font-size: 28px;
		font-weight: 700;
		color: $navy;
	}

	p {
		font-size: 16px;
		margin-top: 10px;
		margin-bottom: 20px;

		.emphasis {
			font-style: italic;
			font-weight: 600;
			color: $light-blue;
		}

		.link {
			font-weight: 600;
			color: $light-blue;

			&:hover {
				color: $gold;
				text-decoration: underline;
			}
		}
	}

	.graphic {
		display: block;
		margin: 0px auto 20px auto;
	}

	.btn {
		text-align: center;
		width: 100%;

		.resend {
			color: $white;
			background-color: $light-blue;

			&:hover {
				color: $orange;
			}
		}
	}

	border-radius: (5px);

	.form-container,
	.message-container {
		border: 1px solid $gray-light;
		overflow: hidden;
		padding: 0;

		border-radius: (10px);
		box-shadow: (5px 5px 5px 0 rgba(0, 0, 0, 0.35));

		.message-box,
		form {
			padding: 20px;

			.slanted {
				font-style: italic;
			}

			p {
				font-size: 16px;
				font-weight: 400;
			}
		}

		h2 {
			background-color: $brand-primary;
			border: 1px solid $brand-primary;
			color: $white;
			font-size: 18px;
			line-height: 20px;
			margin: 0;
			padding: 14px 20px;
		}
	}
}

.import-company {
	.form-container {
		.form-group {
			#regnum-one,
			#regnum-three {
				width: 25%;
			}

			#regnum-two {
				width: 50%;
			}

			.input-group-addon {
				background-color: $orange;

				i {
					color: $white;
				}

				&:hover {
					background-color: $navy;
				}
			}
		}

		.import-table {
			.table {
				th {
					border-radius: (0);
				}
			}
		}
	}
}

.promo-code {
	background: none;
	color: #337ab7;
	border: none;
	height: 35px;
	text-align: left;
	padding-left: 0px;
}

.promo-code-error {
	width: 135px;
	line-height: 8px;
	position: relative;
	left: 345px;
}

.promo-code-button {
	line-height: 1.65;
	padding: 3px 10px;
}

.initial-upgrade-promo-code {
	background: none;
	color: #337ab7;
	border: none;
	height: 34px;
	text-align: right;
	padding-right: 12px;
}

.initial-upgrade-promo-code_error {
	top: 8px;
	width: 135px;
	position: absolute;
	line-height: 8px;
	left: 370px;
}

.modal-dialog {
	margin: 90px auto;
}

.modal-content {
	border: none;

	// extra radius on top corners to hide white background
	border-radius: 15px 15px 10px 10px;
}

.modal-header {
	color: $white;
	background-color: $navy;
	border-radius: 10px 10px 0px 0px;
	border-color: $navy;
	display: block;

	.modal-title {
		font-size: 16px;
		font-weight: 700;
		line-height: 24px;
	}

	.close {
		color: #ccc;
		text-shadow: none;
		margin-top: 0px;
		opacity: 1;

		&:hover {
			color: $white;
		}
	}
}

.modal-body {
	.form-group {
		label {
			line-height: 32px;
			margin-bottom: 6px;

			&.control-label-lite {
				line-height: 20px;

				input[type='radio'] {
					vertical-align: top;
				}

				&.disabled {
					color: #999;
					cursor: not-allowed;
				}
			}

			&.control-label.date-of-birth {
				margin-left: 27%;
			}

			&.control-label.date-issued {
				margin-left: 35%;
			}
		}

		.text-box {
			min-height: 74px;
		}

		.input-group {
			margin-bottom: 10px;
		}
	}

	.modal-footer {
		.btn {
			margin-bottom: 0;
		}

		img.img.cipc-logo {
			margin-bottom: 50px;
		}
	}

	th {
		text-align: left;
		white-space: nowrap;
	}
}

.modal-footer {
	text-align: center;
	border-top: none;

	.btn {
		width: 160px;
		text-align: center;
		margin-bottom: 15px;
		font-weight: 600;
	}

	.btn-nav {
		display: inline-block;
		padding: 6px 20px;
		vertical-align: middle;

		&.btn-prev {
			margin-left: -71px;
		}

		&.btn-next {
			margin-right: -71px;
		}
	}

	.add-company-btn {
		margin-bottom: -10px;
	}

	.btn-orange {
		background-color: $orange;
		border-color: $orange;

		&:hover {
			background-color: $light-blue;
			border-color: $light-blue;
		}

		&:disabled {
			background-color: $gray-light;
			border-color: $gray-light;
		}
	}
}

.search-button {
	color: $white;
	background-color: $orange;
	border-color: $orange;

	&:hover {
		color: $white;
		background-color: $light-blue;
		border-color: $light-blue;
	}

	&:focus {
		color: $white;
	}

	&[disabled] {
		background-color: #999 !important;
		border-color: #999 !important;
		cursor: not-allowed;
	}
}

.company-setup {
	img {
		display: block;
		margin-left: auto;
		margin-right: auto;
	}

	.btn {
		margin-bottom: 25px;
	}
}

.public-nav {
	.login-form {
		margin: 30px 0 10px;
		text-align: right;

		// @include clearfix();

		.btn-login {
			background-color: $orange;
			border: 1px solid $orange;
			color: $white;
			font-size: 13px;
			max-width: 15%;
			padding: 4px 0;
			text-align: center;

			border-radius: (2px);

			&:hover {
				background-color: $light-blue;
				border-color: $light-blue;
			}
		}

		.form-group {
			float: left;
			margin-right: 4%;
			max-width: 38%;

			.forgot-password,
			.label-text {
				color: #ccc;
				font-size: 12px;
				cursor: pointer;
				font-weight: 300;
				vertical-align: text-top;

				&:hover {
					color: $white;
				}
			}

			.form-control {
				border: 1px solid $gray-dark;
				color: $gray-dark;
				font-size: 13px;
				height: 28px;
				margin-bottom: 5px;
				width: 100%;

				border-radius: (2px);
				.date {
					width: 10%;
				}
			}
		}
	}
}

span.invalid-feedback {
	padding-top: 0px;
	padding-bottom: 5px;
}

span.valid-feedback {
	padding-top: 0px;
	padding-bottom: 5px;
}

.invalid-feedback {
	color: $red;
	display: block;
	padding-top: 5px;
	padding-bottom: 5px;
	margin-bottom: 5px;
	span {
		color: black;
	}
}

.valid-feedback {
	color: $green;
	display: inline-block;
	padding-top: 5px;
	padding-bottom: 0px;
	margin-bottom: 0px;
	span {
		color: black;
	}
}

textarea {
	resize: none;
}

fieldset {
	padding: 0;
	margin: 0;
	border: 0;
	// Chrome and Firefox set a `min-width: min-content;` on fieldsets,
	// so we reset that to ensure it behaves more like a standard block element.
	// See https://github.com/twbs/bootstrap/issues/12359.
	min-width: 0;
}

label {
	display: inline-block;
	max-width: 100%; // Force IE8 to wrap long content (see https://github.com/twbs/bootstrap/issues/13141)
	margin-bottom: 5px;
	font-weight: bold;
}

// Normalize form controls
//
// While most of our form styles require extra classes, some basic normalization
// is required to ensure optimum display with or without those classes to better
// address browser inconsistencies.

// Override content-box in Normalize (* isn't specific enough)
input[type='search'] {
	box-sizing: (border-box);
}

// Position radios and checkboxes better
input[type='radio'],
input[type='checkbox'] {
	cursor: pointer;
	margin: 4px 0 0;
	margin-top: 1px; // IE8-9
	line-height: normal;
}

// Set the height of file controls to match text inputs
input[type='file'] {
	display: block;
	width: 100%;
}

// Make range inputs behave like textual form controls
input[type='range'] {
	display: block;
	width: 100%;
}

// Make multiple select elements height not fixed
select[multiple],
select[size] {
	height: auto;
}

// Focus for file, radio, and checkbox
// input[type="file"]:focus,
// input[type="radio"]:focus,
// input[type="checkbox"]:focus {
//   @include tab-focus;
// }

// Common form controls
//
// Shared size and type resets for form controls. Apply `.form-control` to any
// of the following form controls:
//
// select
// textarea
// input[type="text"]
// input[type="password"]
// input[type="datetime"]
// input[type="datetime-local"]
// input[type="date"]
// input[type="month"]
// input[type="time"]
// input[type="week"]
// input[type="number"]
// input[type="email"]
// input[type="url"]
// input[type="search"]
// input[type="tel"]
// input[type="color"]

.form-control {
	display: block;
	width: 100%;
	height: $input-height-base; // Make inputs at least the height of their button counterpart (base line-height + padding + border)
	padding: $padding-base-vertical $padding-base-horizontal;
	font-size: $font-size-base;
	line-height: $line-height-base;
	color: $gray-dark;
	background-color: $input-bg;
	background-image: none; // Reset unusual Firefox-on-Android default style; see https://github.com/necolas/normalize.css/issues/214
	border: 1px solid $input-border;
	border-radius: $input-border-radius; // Note: This has no effect on <select>s in some browsers, due to the limited stylability of <select>s in CSS.
	box-shadow: (inset 0 1px 1px rgba(0, 0, 0, 0.075));
	transition: ('border-color ease-in-out .15s, box-shadow ease-in-out .15s');

	// Customize the `:focus` state to imitate native WebKit styles.
	@mixin form-control-focus() {
		// Placeholder
		@include placeholder() {
			// Disabled and read-only inputs
			//
			// HTML5 says that controls under a fieldset > legend:first-child won't be
			// disabled if the fieldset is disabled. Due to implementation difficulty, we
			// don't honor that edge case; we style them as disabled anyway.
			&[disabled],
			&[readonly],
			fieldset[disabled] & {
				background-color: $input-bg-disabled;
				opacity: 1; // iOS fix for unreadable disabled content; see https://github.com/twbs/bootstrap/issues/11655
			}
		}
	}

	&[disabled],
	fieldset[disabled] & {
		cursor: $cursor-disabled;
	}

	// Reset height for `textarea`s
	textarea {
		height: auto;
	}
}

// Search inputs in iOS
//
// This overrides the extra rounded corners on search inputs in iOS so that our
// `.form-control` class can properly style them. Note that this cannot simply
// be added to `.form-control` as it's not specific enough. For details, see
// https://github.com/twbs/bootstrap/issues/11586.

input[type='search'] {
	-webkit-appearance: none;
}

// Special styles for iOS temporal inputs
//
// In Mobile Safari, setting `display: block` on temporal inputs causes the
// text within the input to become vertically misaligned. As a workaround, we
// set a pixel line-height that matches the given height of the input, but only
// for Safari. See https://bugs.webkit.org/show_bug.cgi?id=139848

@media screen and (-webkit-min-device-pixel-ratio: 0) {
	input[type='date'],
	input[type='time'],
	input[type='datetime-local'],
	input[type='month'] {
		line-height: $input-height-base;

		&.input-sm,
		.input-group-sm & {
			line-height: $input-height-small;
		}

		&.input-lg,
		.input-group-lg & {
			line-height: $input-height-large;
		}
	}
}

// Form groups
//
// Designed to help with the organization and spacing of vertical forms. For
// horizontal forms, use the predefined grid classes.

.form-group {
	margin-bottom: $form-group-margin-bottom;
}

// Checkboxes and radios
//
// Indent the labels to position radios/checkboxes as hanging controls.

.radio,
.checkbox {
	position: relative;
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;

	label {
		min-height: $line-height-computed; // Ensure the input doesn't jump when there is no text
		padding-left: 20px;
		margin-bottom: 0;
		font-weight: normal;
		cursor: pointer;
	}
}

.radio input[type='radio'],
.radio-inline input[type='radio'],
.checkbox input[type='checkbox'],
.checkbox-inline input[type='checkbox'] {
	position: absolute;
	margin-left: -20px;
	margin-top: 4px \9;
}

.radio + .radio,
.checkbox + .checkbox {
	margin-top: -5px; // Move up sibling radios or checkboxes for tighter spacing
}

// Radios and checkboxes on same line
.radio-inline,
.checkbox-inline {
	position: relative;
	display: inline-block;
	line-height: inherit;
	padding-left: 20px;
	margin-bottom: 0;
	vertical-align: middle;
	font-weight: 700;
	cursor: pointer;
}

.radio-inline + .radio-inline,
.checkbox-inline + .checkbox-inline {
	margin-top: 10px;
	margin-left: 10px; // space out consecutive inline controls
}

// Apply same disabled cursor tweak as for inputs
// Some special care is needed because <label>s don't inherit their parent's `cursor`.
//
// Note: Neither radios nor checkboxes can be readonly.
input[type='radio'],
input[type='checkbox'] {
	&[disabled],
	&.disabled,
	fieldset[disabled] & {
		cursor: $cursor-disabled;
	}
}

// These classes are used directly on <label>s
.radio-inline,
.checkbox-inline {
	&.disabled,
	fieldset[disabled] & {
		cursor: $cursor-disabled;
	}
}

// These classes are used on elements with <label> descendants
.radio,
.checkbox {
	&.disabled,
	fieldset[disabled] & {
		label {
			cursor: $cursor-disabled;
		}
	}
}

// Static form control text
//
// Apply class to a `p` element to make any string of text align with labels in
// a horizontal form layout.

.form-control-static {
	// Size it appropriately next to real form controls
	padding-top: ($padding-base-vertical + 1);
	padding-bottom: ($padding-base-vertical + 1);
	// Remove default margin from `p`
	margin-bottom: 0;
	min-height: ($line-height-computed + $font-size-base);

	&.input-lg,
	&.input-sm {
		padding-left: 0;
		padding-right: 0;
	}
}

// Form control sizing
//
// Build on `.form-control` with modifier classes to decrease or increase the
// height and font-size of form controls.
//
// The `.form-group-* form-control` variations are sadly duplicated to avoid the
// issue documented in https://github.com/twbs/bootstrap/issues/15074.

// .input-sm {
// 	.input-size(@input-height-small; @padding-small-vertical; @padding-small-horizontal; @font-size-small; @line-height-small; @input-border-radius-small);
// }

// .form-group-sm {
// 	.form-control {
// 		.input-size(@input-height-small; @padding-small-vertical; @padding-small-horizontal; @font-size-small; @line-height-small; @input-border-radius-small);
// 	}

// 	.form-control-static {
// 		height: @input-height-small;
// 		padding: @padding-small-vertical @padding-small-horizontal;
// 		font-size: @font-size-small;
// 		line-height: @line-height-small;
// 		min-height: (@line-height-computed + @font-size-small);
// 	}
// }

// .input-lg {
// 	.input-size(@input-height-large; @padding-large-vertical; @padding-large-horizontal; @font-size-large; @line-height-large; @input-border-radius-large);
// }

// .form-group-lg {
// 	.form-control {
// 		.input-size(@input-height-large; @padding-large-vertical; @padding-large-horizontal; @font-size-large; @line-height-large; @input-border-radius-large);
// 	}

// 	.form-control-static {
// 		height: @input-height-large;
// 		padding: @padding-large-vertical @padding-large-horizontal;
// 		font-size: @font-size-large;
// 		line-height: @line-height-large;
// 		min-height: (@line-height-computed + @font-size-large);
// 	}
// }

// Form control feedback states
//
// Apply contextual and semantic states to individual form controls.

.has-feedback {
	// Enable absolute positioning
	position: relative;

	// Ensure icons don't overlap text
	.form-control {
		padding-right: ($input-height-base * 1.25);
	}
}

// Feedback icon (requires .glyphicon classes)
.form-control-feedback {
	position: absolute;
	top: 0;
	right: 0;
	z-index: 2; // Ensure icon is above input groups
	display: block;
	width: $input-height-base;
	height: $input-height-base;
	line-height: $input-height-base;
	text-align: center;
	pointer-events: none;
}

.input-lg + .form-control-feedback {
	width: $input-height-large;
	height: $input-height-large;
	line-height: $input-height-large;
}

.input-sm + .form-control-feedback {
	width: $input-height-small;
	height: $input-height-small;
	line-height: $input-height-small;
}

// Reposition feedback icon if input has visible label above
.has-feedback label {
	& ~ .form-control-feedback {
		top: ($line-height-computed + 5); // Height of the `label` and its margin
	}

	&.sr-only ~ .form-control-feedback {
		top: 0;
	}
}

// Help text
//
// Apply to any element you wish to create light text for placement immediately
// below a form control. Use for general help, formatting, or instructional text.

.help-block {
	display: block; // account for any element using help-block
	margin-top: 5px;
	margin-bottom: 10px;
	color: color.adjust($text-color, $lightness: 25%); // lighten the text some for contrast
}

// Inline forms
//
// Make forms appear inline(-block) by adding the `.form-inline` class. Inline
// forms begin stacked on extra small (mobile) devices and then go inline when
// viewports reach <768px.
//
// Requires wrapping inputs and labels with `.form-group` for proper display of
// default HTML form controls and our custom form controls (e.g., input groups).
//
// Heads up! This is mixin-ed into `.navbar-form` in navbars.less.

.form-inline {
	// Kick in the inline
	@media (min-width: $screen-sm-min) {
		// Inline-block all the things for "inline"
		.form-group {
			display: inline-block;
			margin-bottom: 0;
			vertical-align: middle;
		}

		// In navbar-form, allow folks to *not* use `.form-group`
		.form-control {
			display: inline-block;
			width: auto; // Prevent labels from stacking above inputs in `.form-group`
			vertical-align: middle;
		}

		// Make static controls behave like regular ones
		.form-control-static {
			display: inline-block;
		}

		.input-group {
			display: inline-table;
			vertical-align: middle;

			.input-group-addon,
			.input-group-btn,
			.form-control {
				width: auto;
			}

			.input-group-addon-right {
				border-top-right-radius: 0px;
				border-bottom-right-radius: 0px;
			}

			.input-group-addon-left {
				border-top-left-radius: 0px;
				border-bottom-left-radius: 0px;
			}
		}

		// Input groups need that 100% width though
		.input-group > .form-control {
			width: 100%;
		}

		.control-label {
			margin-bottom: 0;
			vertical-align: middle;
		}

		// Remove default margin on radios/checkboxes that were used for stacking, and
		// then undo the floating of radios and checkboxes to match.
		.radio,
		.checkbox {
			display: inline-block;
			margin-top: 0;
			margin-bottom: 0;
			vertical-align: middle;

			label {
				padding-left: 0;
			}
		}

		.radio input[type='radio'],
		.checkbox input[type='checkbox'] {
			position: relative;
		}

		// Re-override the feedback icon.
		.has-feedback .form-control-feedback {
			top: 0;
		}
	}
}

// Horizontal forms
//
// Horizontal forms are built on grid classes and allow you to create forms with
// labels on the left and inputs on the right.

.form-horizontal {
	// Consistent vertical alignment of radios and checkboxes
	//
	// Labels also get some reset styles, but that is scoped to a media query below.
	.radio,
	.checkbox .radio-inline,
	.checkbox-inline {
		margin-top: 0;
		margin-bottom: 0;
		padding-top: ($padding-base-vertical + 1); // Default padding plus a border
	}

	// Account for padding we're adding to ensure the alignment and of help text
	// and other content below items
	.radio,
	.checkbox {
		min-height: ($line-height-computed + ($padding-base-vertical + 1));
	}

	// Make form groups behave like rows
	// .form-group {
	//   .make-row();
	// }

	// Reset spacing and right align labels, but scope to media queries so that
	// labels on narrow viewports stack the same as a default form example.
	@media (min-width: $screen-sm-min) {
		.control-label {
			text-align: right;
			margin-bottom: 0;
			padding-top: ($padding-base-vertical + 1); // Default padding plus a border
		}
	}

	// Validation states
	//
	// Reposition the icon because it's now within a grid column and columns have
	// `position: relative;` on them. Also accounts for the grid gutter padding.
	.has-feedback .form-control-feedback {
		right: calc($grid-gutter-width / 2);
	}

	// Form group sizes
	//
	// Quick utility class for applying `.input-lg` and `.input-sm` styles to the
	// inputs and labels within a `.form-group`.
	.form-group-lg {
		@media (min-width: $screen-sm-min) {
			.control-label {
				padding-top: (($padding-large-vertical * $line-height-large) + 1);
			}
		}
	}

	.form-group-sm {
		@media (min-width: $screen-sm-min) {
			.control-label {
				padding-top: ($padding-small-vertical + 1);
			}
		}
	}
}

.search-input-table {
	display: table;
	width: 100%;

	.search-input {
		display: table-cell;
		vertical-align: top;
		width: 100%;

		input {
			width: 100%;
			border-right: none;
			border-top-right-radius: 0px;
			border-bottom-right-radius: 0px;
			margin-bottom: 0px;
		}
	}

	button.search-button {
		display: table-cell;
		width: 100%;
		line-height: 20px;
		vertical-align: top;
		border-top-left-radius: 0px;
		border-bottom-left-radius: 0px;

		&.searching {
			cursor: not-allowed;
			background-color: $light-blue;
			border-color: $light-blue;
			opacity: 1;
		}
	}
}

.btn-big {
	font-size: 16px !important;
	height: 40px !important;
}
.high-court-centre {
	margin: 20px auto;
	width: 50%;
	box-sizing: border-box;
}

.high-court-img {
	display: block;
	margin: 0 auto;
	width: 50%;
	height: 60%;
}

.info-padding {
	padding-left: 5px;
}

.row.shared-form {
	.col-lg-8 {
		padding: 0 15px;
	}

	h2 {
		font-size: 28px;
		font-weight: 700;
		color: $navy;
	}

	.alert {
		font-size: 14px;

		p {
			margin-top: 0;
			margin-bottom: 0;
		}
	}

	p {
		font-size: 14px;
		margin-top: 10px;
		margin-bottom: 20px;

		&.not-found {
			margin-bottom: 40px;
		}
	}

	.graphic {
		display: block;
		margin: 0px auto 20px auto;
	}
}

// New classes for flex layouts
.flex-row {
	display: flex;
	gap: 5px;
	margin-bottom: 6px;

	.flex-grow {
		flex: 1;
	}

	.flex-group {
		display: flex;
		gap: 5px;
	}
}

.margin-y {
	margin: 6px 0;
}
