<!-- ERRORS -->
<div class="alert alert-danger" role="alert" *ngIf="errors">
	<div *ngFor="let error of errors">{{ error }}</div>
</div>
<div class="col-lg-9">
	<!-- TABLE -->
	<table class="table table-hover" id="fileList">
		<thead>
			<tr>
				<th *ngIf="prefix.split('/').length === 2">Name</th>
				<th *ngIf="prefix.split('/').length > 2" class="return-button">
					<a (click)="onPrevFolder()">
						<app-icon [icon]="sharedService.icons.faReply" />
						{{ folder }}
					</a>
				</th>
				<th>Date</th>
				<th>Size</th>
				<th *ngIf="companyService.isAdmin" width="90">Actions</th>
			</tr>
		</thead>
		<tbody>
			<!-- FOLDERS -->
			<tr *ngFor="let folder of documentService.folders" (click)="onViewFolder(folder.Key)" class="clickable">
				<td>
					<a>
						<app-icon class="folders" [icon]="sharedService.icons.faFolder" />
						{{ folder.name }}
					</a>
				</td>
				<td></td>
				<td>{{ folder.size }}</td>
				<td *ngIf="companyService.isAdmin">
					<button class="dataTableButton">
						<a>
							<app-icon [icon]="sharedService.icons.faFolderOpen" />
						</a>
					</button>
					<button class="dataTableButton" *ngIf="!isDefaultFolder(folder.Key)">
						<a (click)="onDeleteFolder(folder.Key, $event)">
							<app-icon [icon]="sharedService.icons.faTrash" />
						</a>
					</button>
				</td>
			</tr>

			<!-- FILES -->
			<tr *ngFor="let file of documentService.files" class="clickable">
				<td (click)="documentService.openFile(file.Key)">
					<a>{{ file.name }}</a>
				</td>
				<td (click)="documentService.openFile(file.Key)" style="width: 90px">
					{{ file.date }}
				</td>
				<td (click)="documentService.openFile(file.Key)" style="width: 80px">
					{{ file.size }}
				</td>
				<td *ngIf="companyService.isAdmin" class="actions-column">
					<button class="dataTableButton">
						<a (click)="onEditFile(file.Key)">
							<app-icon [icon]="sharedService.icons.faEdit" />
						</a>
					</button>
					<button class="dataTableButton">
						<a (click)="onDeleteFile(file.Key)">
							<app-icon [icon]="sharedService.icons.faTrash" />
						</a>
					</button>
				</td>
			</tr>

			<!-- NO RESULTS -->
			<tr *ngIf="documentService.files.length === 0 && documentService.folders.length === 0">
				<td *ngIf="prefix.split('/').length === 2" colspan="4">Loading documents...</td>
				<td *ngIf="prefix.split('/').length > 2" colspan="4">No documents available.</td>
			</tr>
		</tbody>
	</table>
</div>

<!-- ACTIONS -->
<div class="col-lg-3 actions">
	<h3>Actions</h3>
	<a class="btn btn-primary" (click)="openUploadDocumentsModal()"> Upload Documents </a>
	<a
		*ngIf="companyService.isAdmin && !companyService.isConverted"
		class="btn btn-primary"
		(click)="openRequestDocumentsModal()"
		appButtonClickEvent
	>
		Request Documents
	</a>
	<a
		class="btn btn-primary"
		(click)="openCreateFolderModal()"
		*ngIf="prefix.split('/').length < 3 && companyService.isAdmin"
		appButtonClickEvent
	>
		Create New Folder
	</a>
	<!-- PAID FEATURE -->
	<a
		*ngIf="!accountService.isFree && companyService.isAdmin"
		class="btn btn-primary"
		[routerLink]="['../', 'library']"
		appButtonClickEvent
	>
		View Template Library
	</a>
</div>
