/* Ngx Accordions
------------------------------- */
accordion.panel-group {
	margin-bottom: 13px;

	accordion-group.panel {
		box-shadow: none;
		border: none;

		// styling imported libraries
		::ng-deep {
			div.panel.card.panel-default {
				border-radius: 3px;
				border-top: none;

				div.panel-heading {
					background-color: #fff;
					border-radius: 3px;
					padding: 0;

					div.panel-title {
						font-size: 14px;
						line-height: 32px;

						div.accordion-toggle {
							border: 1px solid #ccc;
							border-radius: 3px;

							a.btn.btn-link {
								font-weight: 400;
								color: #333;
								border: none;
								height: 33px;
								padding: 0;

								div.header-title {
									padding: 6px 12px;
								}

								a.edit-button {
									right: -2px;
									border-radius: 0 3px 3px 0;
									height: 34px;
									width: 40px;
									color: #fff;
									background-color: var(--brand-primary) !important;
									border-color: var(--brand-primary) !important;
									padding: 10px;
									font-size: 14px;

									&:hover {
										background-color: var(--light-blue) !important;
										border-color: var(--light-blue) !important;
									}
								}
							}
						}
					}
				}

				div.panel-body {
					border-top: none;

					.form-group {
						label.control-label {
							&.date-of-birth {
								margin-left: 26%;
							}
							&.date-issued {
								margin-left: 34.5%;
							}
						}

						.form-control {
							&.title {
								display: inline-block;
								vertical-align: bottom;
								margin-right: 1%;
								width: 14%;
							}

							&#firstName.first-name {
								display: inline-block;
								margin-right: 1%;
								width: 42%;
							}

							&.second-name {
								display: inline-block;
								width: 42%;
							}
						}
					}
				}
			}
		}
	}

	&.panel-valid {
		accordion-group.panel {
			::ng-deep {
				div.panel.card.panel-default {
					div.panel-heading {
						div.panel-title {
							div.accordion-toggle {
								border-color: #78fa89;
							}
						}
					}
				}
			}
		}
	}

	&.panel-invalid {
		accordion-group.panel {
			::ng-deep {
				div.panel.card.panel-default {
					div.panel-heading {
						div.panel-title {
							div.accordion-toggle {
								border-color: #fa787e;
							}
						}
					}
				}
			}
		}
	}
}

.btn-block {
	display: flex-root !important;
}
