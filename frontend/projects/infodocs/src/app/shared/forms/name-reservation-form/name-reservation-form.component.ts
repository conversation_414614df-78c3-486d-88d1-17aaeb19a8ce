import { HttpErrorResponse } from '@angular/common/http';
import { Component, EventEmitter, inject, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CipcService } from '@infodocs/cipc/cipc.service';
import { CompanyService } from '@infodocs/company/company.service';
import { CipcTypeId } from '@infodocs/company/variables/enums';
import { SubmitButtonComponent } from '@infodocs/shared/buttons/submit-button/submit-button.component';
import { EVENT_LOGGER_SERVICE } from '@infodocs/shared/event-logging/token';
import { CheckoutFormComponent } from '@infodocs/shared/forms/checkout-form/checkout-form.component';
import { SuccessFormComponent } from '@infodocs/shared/forms/success-form/success-form.component';
import { companyNameValidator, strictCipcNameValidator } from '@infodocs/shared/forms/validators/custom-validators';
import { HasObservablesComponent } from '@infodocs/shared/has-observables.component';
import { CheckoutForm, CheckoutItem } from '@infodocs/shared/interfaces/checkout';
import { SuccessResponse } from '@infodocs/shared/interfaces/success-response';
import { ShouldShowErrorPipe } from '@infodocs/shared/pipes/should-show-error.pipe';
import { SharedService } from '@infodocs/shared/shared.service';
import { AlertModule } from 'ngx-bootstrap/alert';
import { BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

@Component({
	selector: 'app-name-reservation-form',
	templateUrl: './name-reservation-form.component.html',
	styleUrls: [],
	imports: [
		AlertModule,
		FormsModule,
		ReactiveFormsModule,
		SubmitButtonComponent,
		CheckoutFormComponent,
		SuccessFormComponent,
		ShouldShowErrorPipe
	]
})

export class NameReservationFormComponent extends HasObservablesComponent implements OnInit, OnDestroy {
	public state: FormControl<'reserve' | 'checkout' | 'success'> = new FormControl();

	@Output() backButton = new EventEmitter();

	public errorMessage?: string;
	public nameReservationForm: FormGroup = this.fb.group({
		company_id: this.companyService.companyId,
		cipc_type_id: CipcTypeId.name_reservation,
		proposed_name_1: ['', [Validators.required, companyNameValidator(), strictCipcNameValidator()]],
		proposed_name_2: ['', [companyNameValidator(), strictCipcNameValidator()]],
		proposed_name_3: ['', [companyNameValidator(), strictCipcNameValidator()]],
		proposed_name_4: ['', [companyNameValidator(), strictCipcNameValidator()]],
		tracking_no: [''],
	});

	// success + checkout
	public response: SuccessResponse = <SuccessResponse>{};
	public items: CheckoutItem[] = [
		{
			name: 'Name Reservation',
			filter: 'name-reservation',
		},
	];

	private readonly eventLoggerService = inject(EVENT_LOGGER_SERVICE);

	constructor(
		public companyService: CompanyService,
		public sharedService: SharedService,
		public modalService: BsModalService,
		private fb: FormBuilder,
		public cipcService: CipcService
	) {
		super();
	}

	ngOnInit(): void {
		this.state.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(state => {
			const eventData = {
				event: 'Form Submission',
				form: 'nameReservationForm',
				state: state,
				value: this.nameReservationForm.value,
			};
			this.eventLoggerService.logEvent(eventData.event, eventData);
		});

		this.cipcService.transaction.cipc_type_id = CipcTypeId.name_reservation;
		this.cipcService.transaction.company_id = this.companyService.company?.id;

		this.cipcService.fetchOrCreateTransaction(this.cipcService.transaction).pipe(takeUntil(this.destroy$)).subscribe({
			next: response => {
				this.state.setValue('reserve');

				// pre-populate form if already exists
				if (this.cipcService.transaction.json_data) {
					this.nameReservationForm.patchValue(this.cipcService.transaction.json_data);
				}
			},
			error: error => {
				this.errorMessage = 'Failed to fetch or create transaction';
			},
		});

		// Note: Auto-formatting removed to prevent interference with typing spaces
		// The validation will still ensure proper format (letters, numbers, single spaces only)


	}

	override ngOnDestroy() {
		super.ngOnDestroy();
		this.cipcService.clearTransaction();
	}

	async displayCheckout() {
		// check if transaction already paid
		const isPaid = await this.cipcService.isPaid();
		if (isPaid) {
			this.displaySuccess();
			return;
		}

		this.cipcService.submitInformation(this.cipcService.transaction, this.nameReservationForm.value).pipe(takeUntil(this.destroy$)).subscribe({
			next: response => {
				this.state.setValue('checkout');
			},
			error: error => {
				this.errorMessage = error.error.message || 'Failed to submit information.';
			},
		});
	}

	submitPayment(event: CheckoutForm) {
		this.cipcService.submitPayment(this.cipcService.transaction, event).pipe(takeUntil(this.destroy$)).subscribe({
			next: response => this.displaySuccess(),
			error: (err: HttpErrorResponse) => {
				this.errorMessage = err.error.message || 'Failed to submit payment.';
			},
		});
	}

	displaySuccess() {
		this.cipcService.submitToCipc(this.cipcService.transaction).pipe(takeUntil(this.destroy$)).subscribe({
			next: result => {
				this.response = result;
				this.state.setValue('success');
			},
			error: (err: HttpErrorResponse) => {
				this.errorMessage = err.error.message || 'Failed to submit to CIPC.';
			},
		});
	}
}
