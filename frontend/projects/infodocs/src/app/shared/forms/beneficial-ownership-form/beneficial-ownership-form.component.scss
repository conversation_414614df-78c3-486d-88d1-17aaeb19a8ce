.issue-date-verification {
	display: block;
	text-align: right;

	&.valid {
		color: var(--brand-success);
	}

	&.invalid {
		color: var(--brand-danger);
	}
}

/* Ngx Accordions
------------------------------- */

accordion.panel-group {
	margin-bottom: 13px;

	accordion-group.panel {
		box-shadow: none;
		border: none;

		// styling imported libraries
		::ng-deep {
			div.panel.card.panel-default {
				border-radius: 3px;
				border-top: none;

				div.panel-heading {
					background-color: #fff;
					border-radius: 3px;
					padding: 0;

					div.panel-title {
						font-size: 14px;
						line-height: 32px;

						div.accordion-toggle {
							border: 1px solid #ccc;
							border-radius: 3px;

							a.btn.btn-link {
								font-weight: 400;
								color: #333;
								border: none;
								height: 33px;
								padding: 0;

								div.header-title {
									padding: 6px 12px;
								}

								a.edit-button {
									right: -2px;
									border-radius: 0 3px 3px 0;
									height: 34px;
									width: 40px;
									color: #fff;
									background-color: var(--brand-primary) !important;
									border-color: var(--brand-primary) !important;
									padding: 10px;
									font-size: 14px;

									&:hover {
										background-color: var(--light-blue) !important;
										border-color: var(--light-blue) !important;
									}
								}
							}
						}
					}
				}

				div.panel-body {
					border-top: none;

					.date-received {
						margin-left: 34%;
					}
				}
			}
		}
	}

	&.panel-valid {
		accordion-group.panel {
			::ng-deep {
				div.panel.card.panel-default {
					div.panel-heading {
						div.panel-title {
							div.accordion-toggle {
								border-color: var(--brand-success);
							}
						}
					}
				}
			}
		}
	}

	&.panel-invalid {
		accordion-group.panel {
			::ng-deep {
				div.panel.card.panel-default {
					div.panel-heading {
						div.panel-title {
							div.accordion-toggle {
								border-color: var(--brand-danger);
							}
						}
					}
				}
			}
		}
	}

	// p.invalid-feedback {
	// 	padding-top: 0;
	// 	margin-bottom: 15px;
	// }
}

.other-shareholders {
	label {
		font-weight: 400;
		margin-bottom: 0;
	}
	.actions {
		float: right;
		height: 32px;

		a.add-company-btn {
			vertical-align: sub;
		}
	}
}

.input-field-container {
	width: 100%;

	.control-label,
	.inputs-group,
	select,
	input {
		width: 100%;
	}

	&.country-of-origin {
		.form-control {
			margin-bottom: 0px;
		}
	}
}
