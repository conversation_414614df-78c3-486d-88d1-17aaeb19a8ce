/* You can add global styles to this file, and also import other style files */

/* Importing Bootstrap SCSS file and other dependencies */
@use 'assets/plugins/bootstrap.min.css';
@use 'css/scss/variables' as *;
@use 'css/scss/mixins' as *;
@use 'css/scss/account';
@use 'css/scss/company';
@use 'css/scss/dashboard';
@use 'css/scss/documents';
@use 'css/scss/footer';
@use 'css/scss/forms';
@use 'css/scss/grid';
@use 'css/scss/header';
@use 'css/scss/officer';
@use 'css/scss/navigation';
@use 'css/scss/shareholder';
@use 'css/scss/shareclass';
@use 'css/scss/sharetransaction';
@use 'css/scss/sharecertificate';
@use 'css/scss/tables';
@use 'css/scss/portfolio';
@use 'css/scss/app';
@use 'css/scss/datatables';
@use 'css/scss/accordions'; // TODO: [after rebuild] not sure why this is not working for officer-details-form
@use 'css/scss/peach';
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,600;0,700;1,400&display=swap');

/* Global styles */
* {
	outline: none !important;
}

/* Imports
---------------------------- */

/* Globals
---------------------------- */
:root {
	--bs-blue: #0d6efd;
	--bs-indigo: #6610f2;
	--bs-purple: #6f42c1;
	--bs-pink: #d63384;
	--bs-red: #{$brand-danger};
	--bs-orange: #fd7e14;
	--bs-yellow: #ffc107;
	--bs-green: #198754;
	--bs-teal: #20c997;
	--bs-cyan: #0dcaf0;
	--bs-white: #{$white};
	--bs-gray: #6c757d;
	--bs-gray-dark: #343a40;
	--bs-primary: #{$brand-primary};
	--bs-secondary: #6c757d;
	--bs-success: #{$brand-success};
	--bs-info: #{$brand-info};
	--bs-warning: #{$brand-warning};
	--bs-danger: #{$brand-danger};
	--bs-light-blue: #{$light-blue};
	--bs-light: #f8f9fa;
	--bs-dark: #212529;
	--bs-url: #{$url};
	--bs-url-hover: #{$url-hover};
	--bs-font-sans-serif: system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans',
		'Liberation Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
	--bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
	--bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));

	// Brand Colors
	--brand-primary: #{$brand-primary};
	--brand-success: #{$brand-success};
	--brand-warning: #{$brand-warning};
	--brand-danger: #{$brand-danger};
	--brand-info: #{$brand-info};

	// Grayscale
	--gray-base: #{$gray-base};
	--gray-dark: #{$gray-dark};
	--gray: #{$gray};
	--gray-light: #{$gray-light};
	--gray-lighter: #{$gray-lighter};

	// Additional Colors
	--light-blue: #{$light-blue};
	--navy: #{$navy};
	--orange: #{$orange};
	--red: #{$red};
	--gold: #{$gold};
	--green: #{$green};
	--white: #{$white};

	// Links
	--url: #{$url};
	--url-hover: #{$url-hover};

	// Form States
	--state-success-text: #{$state-success-text};
	--state-success-bg: #{$state-success-bg};
	--state-info-text: #{$state-info-text};
	--state-info-bg: #{$state-info-bg};
	--state-warning-text: #{$state-warning-text};
	--state-warning-bg: #{$state-warning-bg};
	--state-danger-text: #{$state-danger-text};
	--state-danger-bg: #{$state-danger-bg};
	--input-border: #{$input-border};
}

body {
	font-family: 'Open Sans', sans-serif;
	display: flex;
	flex-direction: column;
	min-height: 100vh; // Full viewport height
}

a:not([href]):not([class]) {
	color: var(--bs-url);

	&:hover {
		color: $url-hover;
	}
}

.navbar {
	padding: 0 !important;
}

.container {
	padding: 0;
	width: auto;

	// force app-footer to bottom in short pages
	// prettier-ignore

	.row {
		margin: auto;
		max-width: 990px;

		.col-lg-12 {
			padding-left: 15px;
			padding-right: 15px;
		}

		.col-lg-6 {
			&.header-logo {
				float: left;
				padding-left: 15px;
			}
		}

		.col-lg-3 {
			margin-top: 5px;
		}
	}
}

.alignright {
	float: right;
}

.alightleft {
	float: left;
}

.flex-left {
	display: flex;
	justify-content: left;
	gap: $gap-base;
}

.flex-right {
	display: flex;
	align-items: center;
	justify-content: right;
	gap: $gap-base;
}

.flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: $gap-base;
}

.flex-between {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	gap: $gap-base;
}

.round {
	aspect-ratio: 1/1;
	flex-shrink: 0;
	border-radius: 50%;
}

.aligncenter {
	display: block;
	margin: 25px auto;
}

img.alignright {
	margin: 0 0 25px 25px;
}

img.alignleft {
	margin: 0 25px 25px 0;
}

.fa-spin {
	animation-duration: 1s !important;
}

.btn,
.btn:hover {
	transition-duration: 250ms;
	font-weight: 600;
}

.flag {
	display: inline-block;
	height: 12px;
	width: 16px;
}

.st-selected {
	background-color: $navy !important;
	color: $white;
}

a,
a:active,
a:focus,
a:hover {
	cursor: pointer;
	text-decoration: none;
	transition-duration: 250ms;
}

h1,
h2,
h3,
h4,
h5,
h6,
a,
button,
input,
select,
label,
p,
textarea,
span,
td,
th,
.cell {
	font-family: 'Open Sans', sans-serif;
}

ul {
	list-style: none;
	margin: 0;
	padding: 0;
}

#page-wrapper {
	display: flex;
	flex-direction: column;
}

.content {
	flex: 1 0 auto;
}

.footer {
	flex-shrink: 0;
}

.crisp-client .cc-tlyw .cc-qfnu:hover {
	background-color: $light-blue !important;
}

/**
 * Bootstrap overrides
 */
.flex-column {
	flex-direction: column;
}

.w-full {
	width: 100% !important;
}

//// tooltip styling
//.tooltip-container .tooltip-inner {
//	background-color: white !important;
//	color: black !important;
//	opacity: 1 !important;
//	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
//}
//
//.tooltip-container.bs-tooltip-top .arrow::before,
//.tooltip-container.bs-tooltip-bottom .arrow::before,
//.tooltip-container.bs-tooltip-left .arrow::before,
//.tooltip-container.bs-tooltip-right .arrow::before {
//	border-color: white !important;
//	opacity: 1 !important;
//}

.ng-touched.ng-invalid,
.date-selector.ng-touched.ng-invalid select {
	border-color: $red !important;
}

.modal-dialog-bottom-right {
	position: fixed;
	right: 20px;
	bottom: 20px;
	margin: 0;
	max-width: 400px; /* Adjust width as needed */
}

.popover {
	max-width: inherit;
}

.actions {
	.btn {
		margin-bottom: 1rem;
	}
}

.modal-footer {
	display: flex;
	justify-content: center !important;
	align-items: center !important;
}

.search-companies-popover {
	margin-top: 0 !important;

	.popover-arrow {
		display: none !important;
	}

	.popover-body {
		padding: 0 !important;
		padding-bottom: 10px !important;
	}
}
.bg-green {
	background-color: var(--green);
}

.bg-gold {
	background-color: var(--gold);
}

popover-container {
	width: 100% !important;
}

/*
	Form fixes
	Ensure native select dropdown arrows are visible across browsers.
*/
select,
select.form-control {
	-webkit-appearance: menulist;
	-moz-appearance: menulist;
	appearance: auto;
	background-image: initial !important; /* override forms.scss which sets none */
	background-repeat: no-repeat;
	cursor: pointer !important;
}

/* Show arrow in legacy IE/Edge if applicable */
select::-ms-expand {
	display: inline;
}
