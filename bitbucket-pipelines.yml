image: atlassian/default-image:3

options:
  max-time: 10

definitions:
  caches:
    vendor: vendor
    composer: $HOME/.composer/cache
    node: frontend/node_modules

  services:
    mysql:
      image: mysql:5.7
      environment:
        MYSQL_DATABASE: 'infodocs_testing'
        MYSQL_USER: 'infodocs'
        MYSQL_PASSWORD: 'password'
        MYSQL_ROOT_USER: 'root'
        MYSQL_ROOT_PASSWORD: 'password'
        MYSQL_ALLOW_EMPTY_PASSWORD: 'yes'
      ports:
        - 3306:3306

  steps:
    # 🧱 Build Frontend
    - step: &build-frontend
        name: 'Build Frontend'
        image: node:22
        size: 8x
        caches: [node]
        artifacts:
          - frontend/dist/infodocs/browser/**
        script:
          - cd frontend
          - npm ci
          - npm run build:ci

    # 🧪 Test Backend (PHPUnit)
    - step: &test-phpunit
        name: 'Test Backend (phpunit)'
        image: php:8.3-fpm
        size: 8x
        caches: [composer, vendor]
        services:
          - mysql
        script:
          - apt-get update && apt-get install -y libzip-dev git unzip libfreetype6-dev libjpeg62-turbo-dev libpng-dev
          - docker-php-ext-configure gd --with-freetype --with-jpeg > /dev/null
          - docker-php-ext-configure pcntl --enable-pcntl > /dev/null
          - docker-php-ext-install zip gd pcntl pdo_mysql bcmath > /dev/null
          - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
          - echo "memory_limit=512M" > $PHP_INI_DIR/conf.d/memory-limit.ini
          - ln -f -s ci/.env.pipelines.phpunit .env
          - ln -f -s ci/.env.pipelines.phpunit .env.testing
          - composer install --no-plugins --prefer-dist --no-interaction --optimize-autoloader
          - php artisan test

    # 🧩 Full Test Suite (Frontend + Backend)
    - step: &full-test-suite
        name: 'Run Full Test Suite'
        image: php:8.3-fpm
        size: 8x
        caches: [composer, vendor, node]
        services:
          - mysql
        script:
          - apt-get update && apt-get install -y nodejs npm libzip-dev git unzip libfreetype6-dev libjpeg62-turbo-dev libpng-dev curl
          - cd frontend && npm ci && npm run build:ci && cd ..
          - docker-php-ext-configure gd --with-freetype --with-jpeg > /dev/null
          - docker-php-ext-configure pcntl --enable-pcntl > /dev/null
          - docker-php-ext-install zip gd pcntl pdo_mysql bcmath > /dev/null
          - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
          - echo "memory_limit=512M" > $PHP_INI_DIR/conf.d/memory-limit.ini
          - ln -f -s ci/.env.pipelines.phpunit .env
          - ln -f -s ci/.env.pipelines.phpunit .env.testing
          - composer install --no-plugins --prefer-dist --no-interaction --optimize-autoloader
          - php artisan test
        artifacts:
          - frontend/dist/infodocs/**

    # 🚀 Deploy to Staging
    - step: &deploy-staging
        name: 'Deploy to Staging'
        deployment: Test
        trigger: manual
        script:
          - echo "Deploying to Staging"
          
          # Setup SSH
          - mkdir -p ~/.ssh
          - echo "$DEPLOY_SSH_KEY_STAGING" | tr -d '\n' | base64 --decode > ~/.ssh/id_rsa
          - chmod 600 ~/.ssh/id_rsa
          - ssh-keyscan -t rsa staging.infodocs.co.za >> ~/.ssh/known_hosts
          
          # Pull latest code
          - ssh -i ~/.ssh/id_rsa <EMAIL> "
            cd /var/www/infodocs.v2/deploying.staging.infodocs.co.za &&
            git fetch origin &&
            git checkout staging &&
            git reset --hard origin/staging"
          
          # Build frontend
          - ssh -i ~/.ssh/id_rsa <EMAIL> "
            cd /var/www/infodocs.v2/deploying.staging.infodocs.co.za/frontend &&
            npm i && ng build --configuration=staging"
          
          # Build backend
          - ssh -i ~/.ssh/id_rsa <EMAIL> "
            cd /var/www/infodocs.v2/deploying.staging.infodocs.co.za &&
            cp ../staging.infodocs.co.za/.env ./ &&
            composer install &&
            php artisan migrate --force &&
            php artisan nova:publish &&
            php artisan config:cache &&
            php artisan view:cache &&
            php artisan storage:link &&
            php artisan telescope:install &&
            php artisan log-viewer:publish &&
            sudo chmod -R 775 storage"
          
          # Deploy files
          - ssh -i ~/.ssh/id_rsa <EMAIL> "
            rsync -av --delete --exclude='storage' --exclude='.env' --exclude='bootstrap/cache'
            /var/www/infodocs.v2/deploying.staging.infodocs.co.za/
            /var/www/infodocs.v2/staging.infodocs.co.za/"
          
          # Restart queues
          - ssh -i ~/.ssh/id_rsa <EMAIL> "sudo supervisorctl restart all"

    # 🚀 Deploy to UAT
    - step: &deploy-uat
        name: 'Deploy to UAT'
        deployment: Staging
        trigger: manual
        script:
          - echo "Deploying to UAT"
          
          # Setup SSH
          - mkdir -p ~/.ssh
          - echo "$DEPLOY_SSH_KEY_UAT" | tr -d '\n' | base64 --decode > ~/.ssh/id_rsa
          - chmod 600 ~/.ssh/id_rsa
          - ssh-keyscan -t rsa bitbucket.org >> ~/.ssh/known_hosts
          - ssh-keyscan -t rsa uat.infodocs.co.za >> ~/.ssh/known_hosts
          
          # Pull latest code
          - ssh -i ~/.ssh/id_rsa <EMAIL> "
            cd /var/www/infodocs.v2/deploying.uat.infodocs.co.za &&
            git fetch origin &&
            git checkout staging &&
            git reset --hard origin/staging"
          
          # Build frontend
          - ssh -i ~/.ssh/id_rsa <EMAIL> "
            cd /var/www/infodocs.v2/deploying.uat.infodocs.co.za/frontend &&
            npm i && ng build --configuration=uat"
          
          # Build backend
          - ssh -i ~/.ssh/id_rsa <EMAIL> "
            cd /var/www/infodocs.v2/deploying.uat.infodocs.co.za &&
            cp ../uat.infodocs.co.za/.env ./ &&
            composer install &&
            php artisan migrate --force &&
            php artisan nova:publish &&
            php artisan config:cache &&
            php artisan view:cache &&
            php artisan storage:link &&
            php artisan telescope:install &&
            php artisan log-viewer:publish &&
            sudo chmod -R 775 storage"
          
          # Deploy files
          - ssh -i ~/.ssh/id_rsa <EMAIL> "
            rsync -av --delete --exclude='storage' --exclude='.env' --exclude='bootstrap/cache'
            /var/www/infodocs.v2/deploying.uat.infodocs.co.za/
            /var/www/infodocs.v2/uat.infodocs.co.za/"
          
          # Restart queues
          - ssh -i ~/.ssh/id_rsa <EMAIL> "sudo supervisorctl restart all"

    # 🔁 Merge Staging → Master
    - step: &merge-to-master
        name: 'Merge to master'
        trigger: manual
        script:
          - echo "Merging staging to master"
          
          # Setup git config
          - git config --global user.email "<EMAIL>"
          - git config --global user.name "Bitbucket Pipeline"

          # Setup SSH
          - mkdir -p ~/.ssh
          - echo "$MERGE_SSH_KEY" | tr -d '\n' | base64 --decode > ~/.ssh/id_rsa
          - chmod 600 ~/.ssh/id_rsa
          - ssh-keyscan -t rsa,ecdsa,ed25519 bitbucket.org >> ~/.ssh/known_hosts
          # SSH config to bind key to Bitbucket
          - |
            cat > ~/.ssh/config << EOF
            Host bitbucket.org
              IdentityFile ~/.ssh/id_rsa
            EOF
          - chmod 600 ~/.ssh/config

          # Debug SSH setup
          - echo "Testing SSH connection..."
          - ssh -T ***************** || echo "SSH test failed, but continuing..."

          # Clone and merge
          - <NAME_EMAIL>:infodocs/infodocs.v2.git merge-workspace
          - cd merge-workspace
          - git fetch origin
          - git checkout master
          - git reset --hard origin/master
          - git merge origin/staging
          - git remote set-url origin https://x-token-auth:$<EMAIL>/$BITBUCKET_REPO_FULL_NAME.git
          - git push origin master

    # 🚀 Deploy to Production
    - step: &deploy-production
        name: 'Deploy to Production'
        deployment: Production
        script:
          - echo "Deploying to Production"
          
          # Setup SSH
          - mkdir -p ~/.ssh
          - echo "$DEPLOY_SSH_KEY_PROD" | tr -d '\n' | base64 --decode > ~/.ssh/id_rsa
          - chmod 600 ~/.ssh/id_rsa
          - ssh-keyscan -t rsa secure.infodocs.co.za >> ~/.ssh/known_hosts
          
          # Pull latest code
          - ssh -i ~/.ssh/id_rsa <EMAIL> "
            cd /var/www/infodocs.v2/deploying.secure.infodocs.co.za &&
            git fetch origin &&
            git checkout master &&
            git reset --hard origin/master"
          
          # Build frontend
          - ssh -i ~/.ssh/id_rsa <EMAIL> "
            cd /var/www/infodocs.v2/deploying.secure.infodocs.co.za/frontend &&
            npm i && ng build --configuration=production && npm run sentry:sourcemaps"
          
          # Build backend
          - ssh -i ~/.ssh/id_rsa <EMAIL> "
            cd /var/www/infodocs.v2/deploying.secure.infodocs.co.za &&
            cp ../secure.infodocs.co.za/.env ./ &&
            composer install &&
            php artisan migrate --force &&
            php artisan nova:publish &&
            php artisan config:cache &&
            php artisan view:cache &&
            php artisan storage:link &&
            php artisan telescope:install &&
            php artisan log-viewer:publish &&
            sudo chmod -R 775 storage"
          
          # Deploy files
          - ssh -i ~/.ssh/id_rsa <EMAIL> "
            rsync -av --delete --exclude='storage' --exclude='.env' --exclude='bootstrap/cache'
            /var/www/infodocs.v2/deploying.secure.infodocs.co.za/
            /var/www/infodocs.v2/secure.infodocs.co.za/"
          
          # Record deployment commit
          - ssh -i ~/.ssh/id_rsa <EMAIL> "
            echo '${BITBUCKET_COMMIT}' > /var/www/infodocs.v2/secure.infodocs.co.za/deployed_commit.txt"
          
          # Restart queues
          - ssh -i ~/.ssh/id_rsa <EMAIL> "sudo supervisorctl restart all"
          
          # Health check
          - ssh -i ~/.ssh/id_rsa <EMAIL> "
            curl -s -o /dev/null -w '%{http_code}' https://secure.infodocs.co.za/ | grep -q '200' ||
            (echo 'Deployment failed - site is not responding' && exit 1)"

pipelines:
  pull-requests:
    '**':
      - step: *build-frontend
      - step: *test-phpunit

  branches:
    staging:
      - step: *full-test-suite
      - step: *deploy-staging
      - step: *deploy-uat
      - step: *merge-to-master

    master:
      - step: *deploy-production