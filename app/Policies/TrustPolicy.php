<?php

namespace App\Policies;

use App\Enum\StatusEnum;
use App\Models\Trust;
use App\Models\User;
use App\Models\UserTrust;
use Illuminate\Auth\Access\HandlesAuthorization;

class TrustPolicy
{
	use HandlesAuthorization;

	/**
	 * Create a new policy instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		//
	}

	/**
	 * User permission to add trusts
	 *
	 * @param ?User $user
	 * @return bool
	 */
	public function add(?User $user): bool
	{
		// Allow all authenticated users to add trusts
		return true;
	}

	/**
	 * Validate whether a user has access to a trust (from the request)
	 *
	 * @param User $user
	 * * @param Trust $trust
	 * * @return bool
 */
	public function access(User $user, Trust $trust): bool
	{
		return $user->is_support ||
			UserTrust::where([
				'user_id' => $user->id,
				'trust_id' => $trust->id,
				'status_id' => StatusEnum::ACTIVE->value,
			])->exists();
	}

	/**
	 * Check if user can view trust details
	 *
	 * @param User $user
	 * @return bool
	 */
	public static function view(User $user): bool
	{
		if ($user->is_support) {
			return true;
		}
		return false;
	}

	/**
	 * Check if user can update trust details
	 *
	 * @param User $user
	 * @return bool
	 */
	public static function update(User $user): bool
	{
		if ($user->is_support) {
			return true;
		}
		return false;
	}
}
