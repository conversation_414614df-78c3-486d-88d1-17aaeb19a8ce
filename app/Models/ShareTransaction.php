<?php

namespace App\Models;

use App\Enum\ResolutionTypeEnum;
use App\Enum\ShareTransactionEnum;
use App\Http\Traits\ShortUniqueUuidTrait;
use App\ModelFilters\ShareTransactionFilter;
use EloquentFilter\Filterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Auth;
use Jedrzej\Sortable\SortableTrait;

class ShareTransaction extends Model
{
	use HasFactory;
	use ShortUniqueUuidTrait;
	use Filterable;
	use SortableTrait;

	protected $table = 'share_transaction';

	public $timestamps = false;

	protected $dates = ['transaction_date'];

	protected $casts = [
		'transaction_date' => 'date',
		'resolution_type' => ResolutionTypeEnum::class,
	];

	protected $fillable = [
		'user_id',
		'from_shareholding_id',
		'to_shareholding_id',
		'share_class_id',
		'company_id', // Keeping for now for risk mitigation (currently not used)
		'status_id',
		'total_shares',
		'price',
		'transaction_date',
		'id_text',
		'type_id',
		'resolution_type',
	];

	protected array $defaultSortCriteria = ['id,desc'];

	public array $sortable = ['name', 'price', 'id', 'total_shares', 'type', 'transaction_date'];

	protected static function boot()
	{
		parent::boot();

		static::creating(function ($shareTransaction) {
			if (Auth::check() && !$shareTransaction->user_id) {
				$shareTransaction->user_id = Auth::id();
			}
		});
	}

	public function sortType($query, $direction = 'desc')
	{
		return $query->orderBy('type_id', $direction);
	}

	public function sortStatus($query, $direction = 'desc')
	{
		return $query->orderBy('status_id', $direction);
	}

	public function getModelFilterClass(): ?string
	{
		return $this->provideFilter(ShareTransactionFilter::class);
	}

	// Relationships

	public function user(): BelongsTo
	{
		return $this->belongsTo(User::class);
	}

	public function status(): BelongsTo
	{
		return $this->belongsTo(Status::class);
	}

	public function type(): BelongsTo
	{
		return $this->belongsTo(Type::class);
	}

	public function fromShareholding(): BelongsTo
	{
		return $this->belongsTo(Shareholding::class, 'from_shareholding_id', 'id');
	}

	public function toShareholding(): BelongsTo
	{
		return $this->belongsTo(Shareholding::class, 'to_shareholding_id', 'id');
	}

	public function shareClass(): BelongsTo
	{
		return $this->belongsTo(ShareClass::class);
	}

	/**
	 * Get the company through the share class relationship
	 */
	public function getCompanyAttribute()
	{
		return $this->shareClass?->company;
	}

	/**
	 * Get the company ID through the share class relationship
	 */
	public function getCompanyIdAttribute()
	{
		return $this->shareClass?->company_id;
	}

	public function shareholding(): HasOne
	{
		return $this->hasOne(Shareholding::class, 'share_transaction_id', 'id');
	}

	// Attributes

	public function getPricePerShareAttribute(): string
	{
		$pricePerShare = $this->calculatePricePerShare();
		if ($pricePerShare === 0) {
			return '-';
		}
		return 'R ' . number_format($pricePerShare, 2, '.', ',');
	}

	public function getTotalPaidAttribute(): string
	{
		return isset($this->price) && $this->price !== 0 ? 'R ' . $this->price : '-';
	}

	public function getDescriptionAttribute(): string
	{
		return $this->type->description;
	}

	public function getFromShareholdingShareholderNameAttribute(): string
	{
		return $this->fromShareholding->name ?? null;
	}

	// Scopes

	public function scopeAllotments($query)
	{
		return $query->where('share_transaction.type_id', ShareTransactionEnum::SHARE_ALLOT->value);
	}

	public function scopeTransfers($query)
	{
		$query->where('share_transaction.type_id', ShareTransactionEnum::SHARE_TRANSFER->value);
	}

	public function scopeBuybacks($query)
	{
		return $query->where('share_transaction.type_id', ShareTransactionEnum::SHARE_BUYBACK->value);
	}

	// balance transfers (i.e. from one shareholder to the same shareholder)
	public function scopeBalanceTransfers($query)
	{
		return $query
			->join('shareholding as fsh', 'fsh.id', '=', 'share_transaction.from_shareholding_id')
			->join('shareholding as tsh', 'tsh.id', '=', 'share_transaction.to_shareholding_id')
			->where('share_transaction.type_id', ShareTransactionEnum::SHARE_TRANSFER->value)
			->where(function ($q) {
				$q->where(function ($q1) {
					$q1->whereNotNull('fsh.individual_id')->whereColumn('fsh.individual_id', 'tsh.individual_id');
				})
					->orWhere(function ($q2) {
						$q2->whereNotNull('fsh.company_id')->whereColumn('fsh.company_id', 'tsh.company_id');
					})
					->orWhere(function ($q3) {
						$q3->whereNotNull('fsh.trust_id')->whereColumn('fsh.trust_id', 'tsh.trust_id');
					});
			});
	}

	// share transfers that are not balance transfers
	public function scopeRegularTransfers($query)
	{
		$balanceTransferIds = $this->scopeBalanceTransfers(clone $query)->select('share_transaction.id');

		return $query
			->where('share_transaction.type_id', ShareTransactionEnum::SHARE_TRANSFER->value)
			->whereNotIn('share_transaction.id', $balanceTransferIds);
	}

	// Methods

	public function calculatePricePerShare(): float|int
	{
		if (empty($this->price)) {
			return 0;
		}
		$pricePerShare = 0;
		if ($this->total_shares > 0) {
			$pricePerShare = $this->price / $this->total_shares;
		}
		return $pricePerShare;
	}

	/**
	 * @return string followed by number
	 * A = allotment
	 * T = transfer
	 * BOC = balance of certificate
	 * BB = buyback
	 * R = redemption
	 * SC = share conversion
	 */
	public function calculateTransactionDetails($transaction, $company, $shareholderId = null)
	{

		$shareTransactions = ShareTransaction::with(['fromShareholding', 'toShareholding', 'shareClass.company'])
			->where([
				'type_id' => $transaction->type_id,
				'share_class_id' => $transaction->share_class_id,
			])
			->whereHas('shareClass', function ($query) use ($company) {
				$query->where('company_id', $company->id);
			})
			->orderBy('transaction_date')
			->get();

		$prevShareClassId = null;
		$prevDate = null;
		$prevFromCertNo = null;
		$prevToCertNo = null;
		$i = 0;

		if ($transaction->type_id == ShareTransactionEnum::SHARE_ALLOT->value) {
			foreach ($shareTransactions as $shareTransaction) {
				if (
					$shareTransaction->share_class_id == $prevShareClassId &&
					$shareTransaction->transaction_date == $prevDate
				) {
					// Do nothing
				} else {
					$i = $i + 1;
				}

				if ($shareTransaction->id === $transaction->id) {
					$transaction->transaction_number = $i;
					$transaction->to_transfer_no = 'A' . $i;
					return $transaction;
				}
			}
		} else {
			foreach ($shareTransactions as $shareTransaction) {
				if ($shareTransaction->fromShareholding?->shareholder_id === $shareTransaction->toShareholding?->shareholder_id) {
					$this->to_transfer_no  = 'BOC';
				} else {
					$this->to_transfer_no = null;
				}

				if (
					// split transfer
					($shareTransaction->share_class_id === $prevShareClassId &&
						$shareTransaction->transaction_date === $prevDate &&
						$shareTransaction->fromShareholding?->certificate_no == $prevFromCertNo) ||
					// consolidated transfer
					($shareTransaction->transaction_date == $prevDate &&
						$shareTransaction->toShareholding?->certificate_no == $prevToCertNo) ||
					// BOC (do not match above)
					$this->to_transfer_no  === 'BOC'
				) {
					// Do nothing
				} else {
					$i = $i + 1;
				}

				if ($shareTransaction->id === $transaction->id) {
					$transaction->to_date = $transaction->toShareholding?->shareholder_id === $shareholderId
						? $shareTransaction->transaction_date
						: null;
					$transaction->transaction_number = $i;
					$typeId  = $transaction->fromShareholding?->shareholder_id === $transaction->toShareholding?->shareholder_id ? 0 : $transaction->type_id;
					switch ($typeId) {
						case 0:
							$transaction->to_transfer_no = 'BOC';
							break;
						case ShareTransactionEnum::SHARE_ALLOT->value:
							$transaction->to_transfer_no = 'A' . $i;
							break;
						case ShareTransactionEnum::SHARE_TRANSFER->value:
							if ($transaction->to_date != null) {
								$transaction->to_transfer_no = 'T' . $i;
							} else {
								$transaction->from_transfer_no = 'T' . $i;
							}
							break;
						case ShareTransactionEnum::SHARE_CONVERT_CREATE->value:
							$transaction->to_transfer_no = 'SC';
							break;
						case ShareTransactionEnum::SHARE_CONVERT_CANCEL->value:
							$transaction->from_transfer_no = 'SC';
							break;
						case ShareTransactionEnum::SHARE_REDEEM->value:
							$transaction->from_transfer_no = 'R';
							break;
						case ShareTransactionEnum::SHARE_BUYBACK->value:
							$transaction->from_transfer_no = 'BB';
							break;
						default:
							if ($transaction->to_date != null) {
								$transaction->to_transfer_no = 'N/A';
							} else {
								$transaction->from_transfer_no = 'N/A';
							}
							break;
					}
					return $transaction;
				}
				$prevDate = $shareTransaction->transaction_date;
				$prevShareClassId = $shareTransaction->share_class_id;
				$prevFromCertNo = $shareTransaction->fromShareholding?->certificate_no;
				$prevToCertNo = $shareTransaction->toShareholding?->certificate_no;
			}
		}
	}

	/**
	 * @return string
	 */
	public function getBalanceValues($transaction, $shareholderId = null, $toDate = null, &$totalBalance)
	{

		$toShares = $transaction->toShareholding?->shareholder_id === $shareholderId ? $transaction->total_shares : null;
		$fromShares = $transaction->toShareholding?->shareholder_id === $shareholderId ? null : $transaction->total_shares;
		$isToShareholder = $transaction->toShareholding?->shareholder_id == $shareholderId;
		$isFromShareholder = $transaction->fromShareholding?->shareholder_id == $shareholderId;
		$typeId  = $transaction->fromShareholding?->shareholder_id === $transaction->toShareholding?->shareholder_id ? 0 : $transaction->type_id;

		switch ($typeId) {
			case 0:
				$totalBalance;
				break;
			case ShareTransactionEnum::SHARE_ALLOT->value:
				if ($isToShareholder) {
					$totalBalance += $toShares;
				}
				break;

			case ShareTransactionEnum::SHARE_TRANSFER->value:
				if ($toDate != null) {
					$totalBalance += $toShares;
				}
				$totalBalance -= $fromShares;

				break;

			case ShareTransactionEnum::SHARE_CONVERT_CREATE->value:
				if ($isToShareholder) {
					$totalBalance += $toShares;
				}
				break;

			case ShareTransactionEnum::SHARE_CONVERT_CANCEL->value:
				if ($isToShareholder) {
					$totalBalance += $fromShares;
				}
				break;

			case ShareTransactionEnum::SHARE_REDEEM->value:
				if ($isFromShareholder) {
					$totalBalance -= $fromShares;
				}
				break;

			case ShareTransactionEnum::SHARE_BUYBACK->value:
				if ($isFromShareholder) {
					$totalBalance -= $fromShares;
				}
				break;

			default:
				if ($toDate != null) {
					$totalBalance -= $toShares;
				} else {
					$totalBalance -= $fromShares;
				}
				break;
		}


		// Return the final balance formatted as a number
		return number_format($totalBalance, 0);
	}

	/**
	 * check if share transaction was a balance transfer
	 * i.e. from one shareholder to the same shareholder
	 *
	 * @return string
	 */
	public function getTransferType(): string
	{
		if ($this->type_id !== ShareTransactionEnum::SHARE_TRANSFER->value) {
			return '';
		}

		return $this->fromShareholding?->shareholder_id === $this->toShareholding?->shareholder_id ? 'Balance' : 'Transfer';
	}

	public function getCertificateType(): string
	{
		$fromShareholdings = $this->fromShareholding()->get();
		$toShareholdings = $this->toShareholding()->get();

		$isConsolidation = $fromShareholdings->count() > 1;
		$isBalanceTransfer =
			!$isConsolidation &&
			$fromShareholdings->contains(function ($fromShareholding) use ($toShareholdings) {
				return $toShareholdings->contains(function ($toShareholding) use ($fromShareholding) {
					return $fromShareholding->shareholder_id === $toShareholding->shareholder_id;
				});
			});

		if ($isConsolidation) {
			return 'Consolidation';
		} elseif ($isBalanceTransfer) {
			return 'Balance';
		}

		$typeId = $this->type_id;
		if ($typeId === ShareTransactionEnum::SHARE_TRANSFER->value) {
			return 'Transfer';
		} elseif ($typeId === ShareTransactionEnum::SHARE_CONVERT_CREATE->value) {
			return 'Conversion';
		}

		return 'Allotment';
	}
}
