<?php

namespace App\Models;

use App\ModelFilters\TrustFilter;
use EloquentFilter\Filterable;
use App\Http\Traits\ShortUniqueUuidTrait;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Jedrzej\Sortable\SortableTrait;

class Trust extends Model
{
	use HasFactory;
	use ShortUniqueUuidTrait;
	use Filterable;
	use SortableTrait;

	protected $table = 'trust';
	public $timestamps = true;
	public const CREATED_AT = 'created_at';
	public const UPDATED_AT = 'updated_at';

	protected $fillable = [
		'trust_name',
		'trust_number',
		'status_id',
		'trust_master_office_id',
		'financial_year_end',
		'phone',
		'email',
		'tax_number',
		'physical_address_1',
		'physical_address_2',
		'physical_city',
		'physical_province',
		'physical_postal_code',
		'physical_country',
		'pos_address_1',
		'pos_address_2',
		'pos_city',
		'pos_province',
		'pos_postal_code',
		'pos_country',
	];

	protected array $defaultSortCriteria = ['id,asc'];

	public array $sortable = ['trust_name', 'trust_number', 'id', 'status'];

	// Scopes

	public function sortStatus($query, $direction = 'desc')
	{
		return $query->orderBy('status_id', $direction);
	}

	public function getModelFilterClass(): ?string
	{
		return $this->provideFilter(TrustFilter::class);
	}

	public function status(): BelongsTo
	{
		return $this->belongsTo(Status::class);
	}

	public function directorships(): HasMany
	{
		return $this->hasMany(Directorship::class);
	}

	public function shareholdings(): HasMany
	{
		return $this->hasMany(Shareholding::class);
	}

	public function profile(): HasOne
	{
		return $this->hasOne(TrustProfile::class, 'trust_id', 'id');
	}

	public function trusteeships(): HasMany
	{
		return $this->hasMany(Trusteeship::class, 'trust_id', 'id');
	}

	public function beneficiaries(): HasMany
	{
		return $this->hasMany(Beneficiary::class, 'trust_id', 'id');
	}

	public function getNameAttribute(): string
	{
		return $this->trust_name;
	}

	/**
	 * get related companies (current only)
	 * e.g. use $trust->relatedCompanies
	 *
	 * @return Collection
	 */
	public function getRelatedCompaniesAttribute(): Collection
	{
		$shareholdings = $this->shareholdings()
			->current()
			->with('shareTransaction.shareClass')
			->get();

		$shareClassCompanyIds = $shareholdings->pluck('shareTransaction.shareClass.company_id')->toArray();

		return Company::findMany($shareClassCompanyIds);
	}
}
