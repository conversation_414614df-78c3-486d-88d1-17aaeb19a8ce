<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class Beneficiary extends Model
{
	use HasFactory;

	protected $table = 'beneficiary';
	const UPDATE_DATE = 'update_at';
	const CREATED_AT = 'created_at';

	protected $fillable = [
		'status_id',
		'trust_id',
		'entity_id',
		'entity_type',
		'beneficial_interest',
		'email',
		'phone',
		'res_address_1',
		'res_address_2',
		'res_city',
		'role',
		'res_province',
		'res_postal_code',
		'res_country_id',
		'pos_country_id',
		'pos_address_1',
		'pos_address_2',
		'pos_city',
		'pos_province',
		'pos_postal_code',
		'initials',
	];


	public function status(): BelongsTo
	{
		return $this->belongsTo(Status::class);
	}

	public function trust(): BelongsTo
	{
		return $this->belongsTo(Trust::class);
	}

	public function posCountry(): BelongsTo
	{
		return $this->belongsTo(Country::class, 'pos_country_id', 'id');
	}

	public function resCountry(): BelongsTo
	{
		return $this->belongsTo(Country::class, 'res_country_id', 'id');
	}
}
