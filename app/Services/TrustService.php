<?php

namespace App\Services;

use App\Enum\PermissionEnum;
use App\Enum\StatusEnum;
use App\Models\Beneficiary;
use App\Models\Individual;
use App\Models\Trust;
use App\Models\Trusteeship;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class TrustService
{
	/**
	 * Get trustees for a specific trust
	 *
	 * @param int $trustId
	 * @return Collection
	 */
	public function getTrustees(int $trustId): Collection
	{
		return Trusteeship::with(['individual', 'individual.country', 'status'])
			->where('trust_id', $trustId)
			->where('status_id', '!=', StatusEnum::REMOVED->value)
			->get();
	}

	/**
	 * Get beneficiaries for a specific trust
	 *
	 * @param int $trustId
	 * @return Collection
	 */
	public function getBeneficiaries(int $trustId): Collection
	{
		return Beneficiary::with(['status'])
			->where('trust_id', $trustId)
			->where('status_id', '!=', StatusEnum::REMOVED->value)
			->get();
	}

	/**
	 * Create or update a trustee
	 *
	 * @param array $trusteeData
	 * @return Trusteeship
	 */
	public function createOrUpdateTrustee(array $trusteeData): Trusteeship
	{
		// Create or update individual if needed
		if (isset($trusteeData['individual_id'])) {
			$individual = Individual::findOrFail($trusteeData['individual_id']);
		} else {
			$individual = $this->createOrUpdateIndividual($trusteeData);
		}

		// Prepare trusteeship data
		$trusteeshipData = array_merge($trusteeData, [
			'individual_id' => $individual->id,
			'status_id' => $trusteeData['status_id'] ?? StatusEnum::ACTIVE->value,
		]);

		return Trusteeship::updateOrCreate(
			[
				'trust_id' => $trusteeData['trust_id'],
				'individual_id' => $individual->id,
			],
			$trusteeshipData
		);
	}

	/**
	 * Create or update a beneficiary
	 *
	 * @param array $beneficiaryData
	 * @return Beneficiary
	 */
	public function createOrUpdateBeneficiary(array $beneficiaryData): Beneficiary
	{
		// Prepare beneficiary data
		$beneficiaryData = array_merge($beneficiaryData, [
			'status_id' => $beneficiaryData['status_id'] ?? StatusEnum::ACTIVE->value,
		]);

		return Beneficiary::updateOrCreate(
			[
				'trust_id' => $beneficiaryData['trust_id'],
				'entity_id' => $beneficiaryData['entity_id'] ?? StatusEnum::ACTIVE->value,
				'entity_type' => $beneficiaryData['entity_type'] ?? StatusEnum::ACTIVE->value,
			],
			$beneficiaryData
		);
	}

	/**
	 * Create or update an individual
	 *
	 * @param array $individualData
	 * @return Individual
	 */
	public function createOrUpdateIndividual(array $individualData): Individual
	{
		$idNumber = $individualData['id_number'] ?? null;

		if ($idNumber && luhn_check($idNumber)) {
			$individual = Individual::where('id_number', $idNumber)->first();
		} else {
			// Try to find by name if ID number is invalid
			$firstName = $individualData['first_name'] ?? '';
			$surname = $individualData['surname'] ?? '';

			$individual = Individual::where('id_number', $idNumber)
				->where(function ($query) use ($firstName, $surname) {
					$query->where('first_name', 'LIKE', strstr($firstName, ' ', true) ?: $firstName)
						->orWhere('second_name', 'LIKE', substr(strstr($firstName, ' '), 1) ?: null)
						->orWhere('surname', $surname);
				})
				->first();
		}

		if (!$individual) {
			$individual = Individual::create();
		}

		// Update individual data
		$individual->update([
			'first_name' => $individualData['first_name'] ?? $individual->first_name,
			'second_name' => $individualData['second_name'] ?? $individual->second_name,
			'surname' => $individualData['surname'] ?? $individual->surname,
			'status_id' => StatusEnum::ACTIVE->value,
			'citizen' => $individualData['citizen'] ?? $individual->citizen,
			'id_number' => $idNumber ?? $individual->id_number,
			'race' => $individualData['race'] ?? $individual->race,
			'gender' => $individualData['gender'] ?? $individual->gender,
			'disability' => $individualData['disability'] ?? $individual->disability,
			'tax_number' => $individualData['tax_number'] ?? $individual->tax_number,
			'date_of_birth' => $individualData['date_of_birth'] ?? $individual->date_of_birth,
			'country_id' => $individualData['country_id'] ?? $individual->country_id,
		]);

		return $individual;
	}

	/**
	 * Link trust to user with permissions
	 *
	 * @param Trust $trust
	 * @param User $user
	 * @param int $permissionId
	 * @return void
	 */
	public function linkTrustToUser(Trust $trust, User $user, int $permissionId = PermissionEnum::EDIT->value): void
	{
		$user->trusts()->syncWithoutDetaching([
			$trust->id => [
				'status_id' => StatusEnum::ACTIVE->value,
				'permission_id' => $permissionId
			]
		]);
	}

	/**
	 * Soft delete trust and related entities
	 *
	 * @param Trust $trust
	 * @return void
	 */
	public function softDeleteTrust(Trust $trust): void
	{
		DB::transaction(function () use ($trust) {
			// Soft delete trust
			$trust->update(['status_id' => StatusEnum::DELETED->value]);

			// Soft delete related trustees
			$trust->trusteeships()->update(['status_id' => StatusEnum::REMOVED->value]);

			// Soft delete related beneficiaries
			$trust->beneficiaries()->update(['status_id' => StatusEnum::REMOVED->value]);
		});
	}

	/**
	 * Soft delete trustee
	 *
	 * @param Trusteeship $trustee
	 * @return void
	 */
	public function softDeleteTrustee(Trusteeship $trustee): void
	{
		$trustee->update(['status_id' => StatusEnum::REMOVED->value]);
	}

	/**
	 * Soft delete beneficiary
	 *
	 * @param Beneficiary $beneficiary
	 * @return void
	 */
	public function softDeleteBeneficiary(Beneficiary $beneficiary): void
	{
		$beneficiary->update(['status_id' => StatusEnum::REMOVED->value]);
	}
}
