<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BeneficiaryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'status_id' => $this->status_id,
            'status' => $this->status?->name,
            'trust_id' => $this->trust_id,
            'entity_id' => $this->entity_id,
            'entity_type' => $this->entity_type,
            'beneficial_interest' => $this->beneficial_interest,
            'email' => $this->email,
            'phone' => $this->phone,
            'role' => $this->role,
            'initials' => $this->initials,
            'res_address_1' => $this->res_address_1,
            'res_address_2' => $this->res_address_2,
            'res_city' => $this->res_city,
            'res_province' => $this->res_province,
            'res_postal_code' => $this->res_postal_code,
            'res_country_id' => $this->res_country_id,
            'res_country' => $this->resCountry?->name,
            'pos_address_1' => $this->pos_address_1,
            'pos_address_2' => $this->pos_address_2,
            'pos_city' => $this->pos_city,
            'pos_province' => $this->pos_province,
            'pos_postal_code' => $this->pos_postal_code,
            'pos_country_id' => $this->pos_country_id,
            'pos_country' => $this->posCountry?->name,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
