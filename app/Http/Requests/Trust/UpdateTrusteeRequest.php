<?php

namespace App\Http\Requests\Trust;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateTrusteeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'individual_id' => 'int|nullable',
            'trust_id' => 'int|nullable',
            'status_id' => 'int|nullable',
            'date_appointed' => 'date|nullable',
            'date_resigned' => 'nullable|date',
            'initials' => 'string|max:10|nullable',
            'email' => 'email|nullable',
            'phone' => 'string|max:20|nullable',
            'res_country_id' => 'int|nullable',
            'res_address_1' => 'string|max:255|nullable',
            'res_address_2' => 'nullable|string|max:255',
            'res_city' => 'string|max:255|nullable',
            'res_province' => 'string|max:255|nullable',
            'res_postal_code' => 'string|max:20|nullable',
            'pos_country_id' => 'int|nullable',
            'pos_address_1' => 'string|max:255|nullable',
            'pos_address_2' => 'nullable|string|max:255',
            'pos_city' => 'string|max:255|nullable',
            'pos_province' => 'string|max:255|nullable',
            'pos_postal_code' => 'string|max:20|nullable',
            'role' => 'string|max:255|nullable',
        ];
    }
}
