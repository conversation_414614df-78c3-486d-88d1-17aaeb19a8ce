<?php

namespace App\Http\Requests\Trust;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateBeneficiaryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'status_id' => 'int|nullable',
            'trust_id' => 'int|nullable',
            'entity_id' => 'int|nullable',
            'entity_type' => 'int|nullable',
            'beneficial_interest' => 'numeric|nullable',
            'email' => 'email|nullable',
            'phone' => 'string|nullable',
            'res_address_1' => 'string|nullable',
            'res_address_2' => 'string|nullable',
            'res_city' => 'string|nullable',
            'res_province' => 'string|nullable',
            'res_postal_code' => 'string|nullable',
            'res_country_id' => 'int|nullable',
            'pos_address_1' => 'string|nullable',
            'pos_address_2' => 'string|nullable',
            'pos_city' => 'string|nullable',
            'pos_province' => 'string|nullable',
            'pos_postal_code' => 'string|nullable',
            'pos_country_id' => 'int|nullable',
            'individual_id' => 'int|nullable',
            'initials' => 'string|nullable',
            'role' => 'string|nullable',
        ];
    }
}
