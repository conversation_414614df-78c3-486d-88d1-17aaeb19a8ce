<?php

namespace App\Http\Requests\Trust;

use App\Models\TrustMasterOffice;
use Illuminate\Validation\Validator;

class UpdateTrustRequest extends TrustRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'trust_master_office_id' => 'integer|nullable',
            'trust_name' => 'string|max:255|nullable',
            'trust_number' => 'string|max:255|nullable',
            'financial_year_end' => 'string|nullable',
            'phone' => 'string|max:20|nullable',
            'email' => 'email|nullable',
            'tax_number' => 'string|max:20|nullable',
            'physical_address_1' => 'string|max:255|nullable',
            'physical_address_2' => 'nullable|string|max:255',
            'physical_city' => 'string|max:255|nullable',
            'physical_province' => 'string|max:255|nullable',
            'physical_postal_code' => 'string|max:20|nullable',
            'physical_country' => 'string|max:255|nullable',
            'physical_country_id' => 'int|nullable',
            'pos_address_1' => 'string|max:255|nullable',
            'pos_address_2' => 'nullable|string|max:255',
            'pos_city' => 'string|max:255|nullable',
            'pos_province' => 'string|max:255|nullable',
            'pos_postal_code' => 'string|max:20|nullable',
            'pos_country' => 'string|max:255|nullable',
            'pos_country_id' => 'int|nullable',
            'status_id' => 'integer|nullable',
        ]);
    }

    public function withValidator(Validator $validator): void
    {
        $validator->after(function ($validator) {
            // Validate trust_master_office_id if provided
            $trustMasterOfficeId = $this->get('trust_master_office_id');
            if ($trustMasterOfficeId !== null) {
                $exists = TrustMasterOffice::where('id', $trustMasterOfficeId)->exists();
                if (!$exists) {
                    $validator->errors()->add('trust_master_office_id', 'Master office not found.');
                }
            }
        });
    }
}
