<?php

namespace App\Http\Requests\Trust;

use App\Models\Trust;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Http\FormRequest;

class TrustRequest extends FormRequest
{
    /**
     * Handle a failed authorization attempt.
     *
     * @return void
     *
     * @throws AuthorizationException
     */
    protected function failedAuthorization(): void
    {
        throw new AuthorizationException('Access denied. Please confirm you have access to this trust.');
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        $trustId = $this->request->get('trust_id') ?? $this->route('trustId');
        $trust = Trust::find($trustId);
        return $trust && $this->user()->can('access', $trust);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'trust_id' => 'required|integer|exists:trust,id',
        ];
    }

    public function messages(): array
    {
        return [
            'trust_id.required' => 'The trust ID is required.',
            'trust_id.exists' => 'The specified trust does not exist.',
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        // Get trust_id from request data or route parameter (same logic as authorize())
        $trustId = $this->request->get('trust_id') ?? $this->route('trustId');

        // If we have a trust_id, merge it into the request data for validation
        if ($trustId) {
            $this->merge(['trust_id' => $trustId]);
        }
    }

    /**
     * Get the trust model for this request.
     *
     * @return Trust
     */
    public function trust(): Trust
    {
        return Trust::findOrFail($this->trust_id);
    }
}
