<?php

namespace App\Http\Controllers;

use App\Enum\PermissionEnum;
use App\Enum\StatusEnum;
use App\Http\Requests\Trust\CreateTrustRequest;
use App\Http\Requests\Trust\TrustRequest;
use App\Http\Requests\Trust\UpdateTrustRequest;
use App\Http\Resources\TrustDetailResource;
use App\Http\Resources\TrustResource;
use App\Models\Beneficiary;
use App\Models\Individual;
use App\Models\Trust;
use App\Models\TrustHistory;
use App\Models\TrustMasterOffice;
use App\Services\TrustService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use App\Models\Trusteeship;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use Ya<PERSON>ra\DataTables\DataTables;

/**
 * Class TrustController
 *
 * @package App\Http\Controllers
 *
 * This controller handles the HTTP requests for the Trust resource.
 */
class TrustController extends Controller
{
	protected TrustService $trustService;

	public function __construct(TrustService $trustService)
	{
		$this->trustService = $trustService;
	}

	/**
	 * Display a listing of Trust resources.
	 *
	 * @apiEndpoint GET /api/v1/trusts
	 * @apiPermission any
	 *
	 * @return AnonymousResourceCollection
	 */
	public function portfolio(Request $request)
	{
		$trusts = Trust::with(['profile', 'status'])
			->where('status_id', '!=', StatusEnum::DELETED->value)
			->filter($request->all())
			->sorted()
			->paginate($request->get('length'));

		return TrustResource::collection($trusts);
	}

	/**
	 * Show the form for creating a new Trust.
	 *
	 * @apiEndpoint POST /api/v1/trusts/create
	 * @apiPermission any
	 *
	 * @return JsonResponse
	 */
	public function create(CreateTrustRequest $request): JsonResponse
	{
		$trust = $request['trust'];
		// Prepare data for insertion
		$trustData = [
			'trust_master_office_id' => $trust['trust_master_office_id'],
			'status_id' => StatusEnum::ACTIVE->value,
			'financial_year_end' => $trust['financial_year_end'],
			'phone' => $trust['phone'],
			'email' => $trust['email'],
			'tax_number' => $trust['tax_number'],
			'physical_address_1' => $trust['physical_address_1'],
			'physical_address_2' => $trust['physical_address_2'],
			'physical_city' => $trust['physical_city'],
			'physical_province' => $trust['physical_province'],
			'physical_postal_code' => $trust['physical_postal_code'],
			'physical_country' => $trust['physical_country'],
			'physical_country_id' => $trust['physical_country_id'],
			'pos_address_1' => $trust['pos_address_1'],
			'pos_address_2' => $trust['pos_address_2'],
			'pos_city' => $trust['pos_city'],
			'pos_province' => $trust['pos_province'],
			'pos_postal_code' => $trust['pos_postal_code'],
			'pos_country' => $trust['pos_country'],
		];

		// Insert the trust into the database
		$trust = Trust::updateOrCreate([
			'trust_name' => $trust['trust_name'],
			'trust_number' => $trust['trust_number'],
		], $trustData);

		// Link trust to trustee
		foreach ($request['trustees'] as $trustee) {
			// create/update individual if id_number passes luhn check etc
			$name = $trustee['first_name'];
			$surname = $trustee['surname'];
			if (luhn_check($trustee['id_number'])) {
				$individual = Individual::where('id_number', $trustee['id_number'])->first();
			} else {
				$individual = Individual::where('id_number', $trustee['id_number'])
					->where(function ($query) use ($name, $surname) {
						$query
							->where('first_name', 'LIKE', strstr($name, ' ', true) ?: $name)
							->orWhere('second_name', 'LIKE', substr(strstr($name, ' '), 1) ?: null)
							->orWhere('second_name', 'LIKE', substr(strstr($name, ' '), 1) ?: '')
							->orWhere('surname', $surname);
					})
					->first();
			}

			if ($individual === null) {
				$individual = Individual::create();
			}

			$individual->first_name = $trustee['first_name'] ?? null;
			$individual->second_name = $trustee['second_name'] ?? null;
			$individual->surname = $trustee['surname'] ?? null;
			$individual->status_id = StatusEnum::ACTIVE->value;
			$individual->citizen = $trustee['citizen'] ?? 0;
			$individual->id_number = $trustee['id_number'] ?? null;
			$individual->race = $trustee['race'] ?? null;
			$individual->gender = $trustee['gender'] ?? null;
			$individual->disability = $trustee['disability'] ?? 0;
			$individual->tax_number = $trustee['tax_number'] ?? null;
			$individual->date_of_birth = $trustee['date_of_birth'] ?? null;
			$individual->country_id = $trustee['country_id'] ?? null;


			$individual->save();

			// create/update trustee
			Trusteeship::updateOrCreate(
				[
					'trust_id' => $trust->id,
					'individual_id' => $individual->id,
					'entity_id' => StatusEnum::ACTIVE->value,
					'entity_type' => StatusEnum::ACTIVE->value
				],
				$trustee
			);
		}

		// Link trust to beneficiaries
		foreach ($request['beneficiaries'] as $beneficiary) {
			Beneficiary::updateOrCreate(
				[
					'trust_id' => $trust->id,
					'entity_id' => StatusEnum::ACTIVE->value,
					'entity_type' => StatusEnum::ACTIVE->value
				],
				$beneficiary
			);
		}

		// Linking trust to user and account if user has one
		$user = $request->user();
		if ($user) {
			$user->trusts()->attach($trust->id, [
				'status_id' => StatusEnum::ACTIVE->value,
				'permission_id' => PermissionEnum::EDIT->value
			]);
		}

		// Return a successful response
		return response()->json([
			'message' => 'Trust created successfully.'
		], ResponseAlias::HTTP_CREATED);
	}

	/**
	 * Store a newly created Trust in storage.
	 *
	 * @apiEndpoint POST /api/v1/trusts
	 * @apiPermission any
	 * @apiParam {string} name Name of the Trust
	 *
	 * @param Request $request
	 * @return Response
	 * @throws Exception
	 */
	public function store(Request $request)
	{
		throw new Exception('Not implemented');
	}

	/**
	 * Display the specified Trust.
	 *
	 * @apiEndpoint GET /api/v1/trusts/{id}
	 * @apiPermission any
	 * @apiParam {int} id Trust ID
	 *
	 * @param TrustRequest $request
	 * @param $trustId
	 * @return TrustDetailResource
	 */
	public function show(TrustRequest $request, $trustId)
	{
		// Fetch the trust with relationships
		$trust = Trust::with(['profile', 'status', 'trusteeships.individual', 'beneficiaries.individual'])
			->findOrFail($trustId);

		return new TrustDetailResource($trust);
	}

	/**
	 * Show the form for editing the specified Trust.
	 *
	 * @apiEndpoint GET /api/v1/trusts/{id}/edit
	 * @apiPermission any
	 * @apiParam {int} id Trust ID
	 *
	 * @param int $id
	 * @return Response
	 * @throws Exception
	 */
	public function edit($id)
	{
		throw new Exception('Not implemented');
	}

	/**
	 * Update the specified Trust in storage.
	 *
	 * @apiEndpoint PUT/PATCH /api/v1/trusts/{id}
	 * @apiPermission any
	 * @apiParam {int} id Trust ID
	 * @apiParam {string} name Name of the Trust
	 *
	 * @param UpdateTrustRequest $request
	 * @param int $id
	 * @return JsonResponse
	 */
	public function update(UpdateTrustRequest $request, $id): JsonResponse
	{
		$trust = $request->trust();

		// Update trust data
		$trust->update($request->validated());

		// Update trust profile if it exists
		if ($trust->profile) {
			$profileData = $request->only([
				'tax_number',
				'principal_email',
				'principal_phone',
				'physical_address_1',
				'physical_address_2',
				'physical_city',
				'physical_province',
				'physical_postal_code',
				'physical_country_id',
				'pos_address_1',
				'pos_address_2',
				'pos_city',
				'pos_province',
				'pos_postal_code',
				'pos_country_id'
			]);

			$trust->profile->update(array_filter($profileData));
		}

		return response()->json([
			'message' => 'Trust updated successfully.',
			'trust' => new TrustDetailResource($trust->fresh())
		], ResponseAlias::HTTP_OK);
	}

	/**
	 * Remove the specified Trust from storage.
	 *
	 * @apiEndpoint DELETE /api/v1/trusts/{id}
	 * @apiPermission any
	 * @apiParam {int} id Trust ID
	 *
	 * @param int $id
	 * @return JsonResponse
	 */
	public function destroy(TrustRequest $request, $id): JsonResponse
	{
		$trust = $request->trust();

		// Use service to handle soft delete
		$this->trustService->softDeleteTrust($trust);

		return response()->json([
			'message' => 'Trust removed successfully.'
		], ResponseAlias::HTTP_NO_CONTENT);
	}

	/**
	 * Get all trustees for a specific trust.
	 *
	 * @param TrustRequest $request
	 * @param int $trustId
	 * @return Response
	 * @throws \Yajra\DataTables\Exceptions\Exception
	 */
	public function getTrustees(TrustRequest $request, int $trustId)
	{
		return DataTables::make($this->trustService->getTrustees($trustId))
			->toJson()
			->getData();
	}

	/**
	 * Get all beneficiaries for a specific trust.
	 *
	 * @param TrustRequest $request
	 * @param int $trustId
	 * @return Response
	 * @throws \Yajra\DataTables\Exceptions\Exception
	 */
	public function getBeneficiaries(TrustRequest $request, int $trustId)
	{
		return DataTables::make($this->trustService->getBeneficiaries($trustId))
			->toJson()
			->getData();
	}

	public function history(Request $request, int $trustId)
	{
		$history = TrustHistory::where([
			'status_id' => StatusEnum::ACTIVE,
			'trust_id' => $trustId,
		])
			->orderBy('change_date', 'desc')
			->get()
			->map(function ($history) {
				return [
					'description' => $history->description,
					'change_date' => $history->change_date,
					'trust_id' => $history->trust_id,
				];
			});
		return response()->json($history);
	}
}
