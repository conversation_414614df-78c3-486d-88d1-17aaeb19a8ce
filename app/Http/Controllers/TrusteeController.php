<?php

namespace App\Http\Controllers;

use App\Enum\StatusEnum;
use App\Http\Requests\Trust\CreateTrusteeRequest;
use App\Http\Requests\Trust\TrustRequest;
use App\Http\Requests\Trust\UpdateTrusteeRequest;
use App\Http\Resources\TrusteeResource;
use App\Models\Trusteeship;
use App\Services\TrustService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

/**
 * Class TrusteeController
 *
 * @package App\Http\Controllers
 *
 * This controller handles the HTTP requests for the Trustee resource.
 */
class TrusteeController extends Controller
{
	protected TrustService $trustService;

	public function __construct(TrustService $trustService)
	{
		$this->trustService = $trustService;
	}
	/**
	 * Display a listing of Trustees.
	 *
	 * @apiEndpoint GET /api/v1/trusts/{trust}/trustees
	 * @apiPermission any
	 * @apiParam {int} trust Trust ID
	 *
	 * @param TrustRequest $request
	 * @param int $trustId
	 * @return JsonResponse
	 */
	public function getTrustees(TrustRequest $request, int $trustId): JsonResponse
	{
		$trustees = $this->trustService->getTrustees($trustId);

		return TrusteeResource::collection($trustees)->response();
	}

	/**
	 * Show the form for creating a new Trustee.
	 *
	 * @apiEndpoint GET /api/v1/trusts/{trust}/trustees/create
	 * @apiPermission any
	 * @apiParam {int} Trust ID
	 *
	 * @return JsonResponse
	 */
	public function create(CreateTrusteeRequest $request): JsonResponse
	{
		// filter data
		$trusteeData = [
			'individual_id' => $request->individual_id,
			'trust_id' => $request->trust_id,
			'entity_id' => StatusEnum::ACTIVE->value,
			'entity_type' => StatusEnum::ACTIVE->value
		];

		// remaining data
		$additionalData = [
			'status_id' => $request->status_id,
			'date_appointed' => $request->date_appointed,
			'date_resigned' => $request->date_resigned,
			'initials' => $request->initials,
			'email' => $request->email,
			'phone' => $request->phone,
			'res_address_1' => $request->res_address_1,
			'res_address_2' => $request->res_address_2,
			'res_city' => $request->res_city,
			'res_province' => $request->res_province,
			'role' => $request->role,
			'res_postal_code' => $request->res_postal_code,
			'res_country_id' => $request->res_country_id,
			'pos_country_id' => $request->pos_country_id,
			'pos_address_1' => $request->pos_address_1,
			'pos_address_2' => $request->pos_address_2,
			'pos_city' => $request->pos_city,
			'pos_province' => $request->pos_province,
			'pos_postal_code' => $request->pos_postal_code,
			'trusteeship_id' => $request->trusteeship_id,
		];

		$trustee = Trusteeship::updateOrCreate($trusteeData, $additionalData);

		$responseStatus = $trustee->wasRecentlyCreated ? ResponseAlias::HTTP_CREATED : ResponseAlias::HTTP_OK;
		$message = $trustee->wasRecentlyCreated ? 'Trustee created successfully.' : 'Trustee already exists.';

		return response()->json(['message' => $message, 'trusteeId' => $trustee->id], $responseStatus);
	}

	/**
	 * Store a newly created Trustee in storage.
	 *
	 * @apiEndpoint POST /api/v1/trusts/{trust}/trustees
	 * @apiPermission any
	 * @apiParam {int} Trust ID
	 * @apiParamExample {json} Request-Example:
	 *      {
	 *          "name": "Trustee name"
	 *      }
	 * @param  \Illuminate\Http\Request  $request
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function store(Request $request)
	{
		//
	}

	/**
	 * Display the specified Trustee.
	 *
	 * @apiEndpoint GET /api/v1/trusts/{trust}/trustees/{trustee}
	 * @apiPermission any
	 * @apiParam {int} trust Trust ID
	 * @apiParam {int} trustee Trustee ID
	 *
	 * @param TrustRequest $request
	 * @param int $trustId
	 * @param int $trusteeId
	 * @return JsonResponse
	 */
	public function show(TrustRequest $request, int $trustId, int $trusteeId): JsonResponse
	{
		$trustee = Trusteeship::with(['individual', 'individual.country', 'status'])
			->where('trust_id', $trustId)
			->where('id', $trusteeId)
			->where('status_id', '!=', StatusEnum::REMOVED->value)
			->firstOrFail();

		return response()->json([
			'trustee' => new TrusteeResource($trustee)
		]);
	}

	/**
	 * Show the form for editing the specified Trustee.
	 *
	 * @apiEndpoint GET /api/v1/trusts/{trust}/trustees/{trustee}/edit
	 * @apiPermission any
	 * @apiParam {int} trust Trust ID
	 * @apiParam {int} trustee Trustee ID
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function edit($id)
	{
		//
	}

	/**
	 * Update the specified Trustee in storage.
	 *
	 * @apiEndpoint PUT/PATCH /api/v1/trusts/{trust}/trustees/{trustee}
	 * @apiPermission any
	 * @apiParam {int} trust Trust ID
	 * @apiParam {int} trustee Trustee ID
	 * @apiParamExample {json} Request-Example:
	 *      {
	 *          "name": "New Trustee Name"
	 *      }
	 * @param UpdateTrusteeRequest $request
	 * @param int $id
	 * @return JsonResponse
	 */
	public function update(UpdateTrusteeRequest $request, int $trustId, int $trusteeId): JsonResponse
	{
		$trustee = Trusteeship::where('trust_id', $trustId)
			->where('id', $trusteeId)
			->where('status_id', '!=', StatusEnum::REMOVED->value)
			->firstOrFail();

		$trustee->update($request->validated());

		return response()->json([
			'message' => 'Trustee updated successfully.',
			'trustee' => new TrusteeResource($trustee->fresh())
		], ResponseAlias::HTTP_OK);
	}

	/**
	 * Remove the specified Trustee from storage.
	 *
	 * @apiEndpoint DELETE /api/v1/trusts/{trust}/trustees/{trustee}
	 * @apiPermission any
	 * @apiParam {int} trust Trust ID
	 * @apiParam {int} trustee Trustee ID
	 *
	 * @param int $id
	 * @return JsonResponse
	 */
	public function destroy(int $trustId, int $trusteeId): JsonResponse
	{
		$trustee = Trusteeship::where('trust_id', $trustId)
			->where('id', $trusteeId)
			->where('status_id', '!=', StatusEnum::REMOVED->value)
			->firstOrFail();

		// Use service to handle soft delete
		$this->trustService->softDeleteTrustee($trustee);

		return response()->json([
			'message' => 'Trustee removed successfully.'
		], ResponseAlias::HTTP_NO_CONTENT);
	}
}
