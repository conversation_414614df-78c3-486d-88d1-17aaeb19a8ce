<?php

namespace App\Http\Controllers;

use App\Enum\StatusEnum;
use App\Http\Requests\BeneficiaryRequest;
use App\Http\Requests\Trust\TrustRequest;
use App\Http\Requests\Trust\UpdateBeneficiaryRequest;
use App\Http\Resources\BeneficiaryResource;
use App\Models\Beneficiary;
use App\Models\Individual;
use App\Services\TrustService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

/**
 * Class BeneficiaryController
 *
 * @package App\Http\Controllers
 *
 * This controller handles the HTTP requests for the Beneficiary resource.
 */
class BeneficiaryController extends Controller
{
	protected TrustService $trustService;

	public function __construct(TrustService $trustService)
	{
		$this->trustService = $trustService;
	}
	/**
	 * Display a listing of Beneficiaries.
	 *
	 * @apiEndpoint GET /api/v1/trusts/{trust}/beneficiaries
	 * @apiPermission any
	 * @apiParam {int} trust Trust ID
	 *
	 * @param TrustRequest $request
	 * @param int $trustId
	 * @return JsonResponse
	 */
	public function getBeneficiaries(TrustRequest $request, int $trustId): JsonResponse
	{
		$beneficiaries = $this->trustService->getBeneficiaries($trustId);

		return BeneficiaryResource::collection($beneficiaries)->response();
	}

	/**
	 * Show the form for creating a new Beneficiary.
	 *
	 * @apiEndpoint GET /api/v1/trusts/{trust}/beneficiaries/create
	 * @apiPermission any
	 * @apiParam {int} Trust ID
	 *
	 * @return JsonResponse
	 */
	public function create(BeneficiaryRequest $request): JsonResponse
	{
		$beneficiary = Beneficiary::updateOrCreate([
			'trust_id' => $request->trust_id,
			'entity_id' => $request->entity_id,
			'entity_type' => $request->entity_type,
		], [
			'status_id' => $request->status_id,
			'beneficial_interest' => $request->beneficial_interest,
			'email' => $request->email,
			'phone' => $request->phone,
			'role' => $request->role,
			'initials' => $request->initials,
			'res_address_1' => $request->res_address_1,
			'res_address_2' => $request->res_address_2,
			'res_city' => $request->res_city,
			'res_province' => $request->res_province,
			'res_postal_code' => $request->res_postal_code,
			'res_country_id' => $request->res_country_id,
			'pos_address_1' => $request->pos_address_1,
			'pos_address_2' => $request->pos_address_2,
			'pos_city' => $request->pos_city,
			'pos_province' => $request->pos_province,
			'pos_postal_code' => $request->pos_postal_code,
			'pos_country_id' => $request->pos_country_id,
		]);

		return response()->json([
			'success' => true,
			'message' => 'Beneficiary added successfully',
			'beneficiaryId' => $beneficiary->id,
		], $beneficiary->wasRecentlyCreated ? ResponseAlias::HTTP_CREATED : ResponseAlias::HTTP_OK);
	}

	/**
	 * Store a newly created Beneficiary in storage.
	 *
	 * @apiEndpoint POST /api/v1/trusts/{trust}/beneficiaries
	 * @apiPermission any
	 * @apiParam {int} trust Trust ID
	 * @apiParamExample {json} Request-Example:
	 *      {
	 *          "name": "Beneficiary Name"
	 *      }
	 * @param  \Illuminate\Http\Request  $request
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function store(Request $request)
	{
		//
	}

	/**
	 * Display the specified Beneficiary.
	 *
	 * @apiEndpoint GET /api/v1/trusts/{trust}/beneficiaries/{beneficiary}
	 * @apiPermission any
	 * @apiParam {int} trust Trust ID
	 * @apiParam {int} beneficiary Beneficiary ID
	 *
	 * @param TrustRequest $request
	 * @param int $trustId
	 * @param int $beneficiaryId
	 * @return JsonResponse
	 */
	public function show(TrustRequest $request, int $trustId, int $beneficiaryId): JsonResponse
	{
		$beneficiary = Beneficiary::with(['status'])
			->where('trust_id', $trustId)
			->where('id', $beneficiaryId)
			->where('status_id', '!=', StatusEnum::REMOVED->value)
			->firstOrFail();

		return response()->json([
			'beneficiary' => new BeneficiaryResource($beneficiary)
		]);
	}

	/**
	 * Show the form for editing the specified Beneficiary.
	 *
	 * @apiEndpoint GET /api/v1/trusts/{trust}/beneficiaries/{beneficiary}/edit
	 * @apiPermission any
	 * @apiParam {int} trust Trust ID
	 * @apiParam {int} beneficiary Beneficiary ID
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function edit($id)
	{
		//
	}

	/**
	 * Update the specified Beneficiary in storage.
	 *
	 * @apiEndpoint PUT/PATCH /api/v1/trusts/{trust}/beneficiaries/{beneficiary}
	 * @apiPermission any
	 * @apiParam {int} trust Trust ID
	 * @apiParam {int} beneficiary Beneficiary ID
	 * @apiParamExample {json} Request-Example:
	 *      {
	 *          "name": "New Beneficiary Name"
	 *      }
	 * @param UpdateBeneficiaryRequest $request
	 * @param int $id
	 * @return JsonResponse
	 */
	public function update(UpdateBeneficiaryRequest $request, int $trustId, int $beneficiaryId): JsonResponse
	{
		$beneficiary = Beneficiary::where('trust_id', $trustId)
			->where('id', $beneficiaryId)
			->where('status_id', '!=', StatusEnum::REMOVED->value)
			->firstOrFail();

		$beneficiary->update($request->validated());

		return response()->json([
			'message' => 'Beneficiary updated successfully.',
			'beneficiary' => new BeneficiaryResource($beneficiary->fresh())
		], ResponseAlias::HTTP_OK);
	}

	/**
	 * Remove the specified Beneficiary from storage.
	 *
	 * @apiEndpoint DELETE /api/v1/trusts/{trust}/beneficiaries/{beneficiary}
	 * @apiPermission any
	 * @apiParam {int} trust Trust ID
	 * @apiParam {int} beneficiary Beneficiary ID
	 *
	 * @param int $id
	 * @return JsonResponse
	 */
	public function destroy(int $trustId, int $beneficiaryId): JsonResponse
	{
		$beneficiary = Beneficiary::where('trust_id', $trustId)
			->where('id', $beneficiaryId)
			->where('status_id', '!=', StatusEnum::REMOVED->value)
			->firstOrFail();

		// Use service to handle soft delete
		$this->trustService->softDeleteBeneficiary($beneficiary);

		return response()->json([
			'message' => 'Beneficiary removed successfully.'
		], ResponseAlias::HTTP_NO_CONTENT);
	}
}
