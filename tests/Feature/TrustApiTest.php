<?php

namespace Tests\Feature;

use App\Enum\StatusEnum;
use App\Models\Beneficiary;
use App\Models\Individual;
use App\Models\Trust;
use App\Models\Trusteeship;
use App\Models\TrustHistory;
use App\Models\TrustMasterOffice;
use App\Models\User;
use App\Models\UserTrust;
use Database\Factories\TrusteeshipFactory;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use Tests\TestCase;

class TrustApiTest extends TestCase
{
	use RefreshDatabase, WithFaker;

	protected string $baseUrl;

	public function setUp(): void
	{
		parent::setUp();
		$this->baseUrl = sprintf('api/%s/trust', config('API_VERSION', 'v1'));
	}

	/**
	 * Test that a user cannot get trusts if not logged in
	 */
	public function test_cannot_get_trusts_if_not_logged_in()
	{
		$response = $this->getJson($this->baseUrl);

		$response->assertStatus(Response::HTTP_UNAUTHORIZED);
	}

	/**
	 * List of paginated trusts
	 */
	public function test_can_get_trusts(): void
	{
		/** @var User $user */
		/** @var User $user */
		$user = User::factory()->create();

		Trust::factory()->forUser($user)->count(60)->create();

		$this->actingAs($user)->get(sprintf('%s?length=60', $this->baseUrl))
			->assertJsonCount(60, 'data');
	}

	/**
	 * Search trusts
	 */
	public function test_can_search_trust(): void
	{
		/** @var User $user */
		/** @var User $user */
		$user = User::factory()->create();

		Trust::factory()->forUser($user)->count(5)->create();

		$trust = Trust::factory([
			'trust_name' => ucwords('Unique TRust'),
			'trust_number' => '123566777',
			'status_id' => StatusEnum::PARTIAL
		])->create();

		$this->actingAs($user)->getJson(sprintf('%s?search=%s', $this->baseUrl, $trust->name))
			->assertStatus(Response::HTTP_OK)
			->assertJsonCount(1, 'data');

		$this->actingAs($user)->getJson(sprintf('%s?search=%s', $this->baseUrl, substr($trust->name, 0, 3)))
			->assertStatus(Response::HTTP_OK)
			->assertJsonCount(1, 'data');

		$this->actingAs($user)->getJson(sprintf('%s?status_id=5', $this->baseUrl))
			->assertStatus(Response::HTTP_OK)
			->assertJsonCount(1, 'data');

		$this->actingAs($user)->getJson(sprintf('%s?trust_number=123566777', $this->baseUrl))
			->assertStatus(Response::HTTP_OK)
			->assertJsonCount(1, 'data');

		$this->actingAs($user)->getJson(sprintf('%s?status_id=4', $this->baseUrl))
			->assertStatus(Response::HTTP_OK)
			->assertJsonCount(0, 'data');
	}

	/**
	 * Show trust
	 */
	public function test_can_get_trust_details(): void
	{
		/** @var User $user */
		$user = User::factory()->create();

		$trust = Trust::factory()->withProfile()->create();

		UserTrust::factory()->create([
			'user_id' => $user->id,
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		$this->actingAs($user)->get(sprintf('%s/%d', $this->baseUrl, $trust->id))
			->assertStatus(Response::HTTP_OK)
			->assertJsonFragment([
				"id" => $trust->id,
				"trust_name" =>  $trust->trust_name,
				"trust_number" =>  $trust->trust_number,
				'tax_number' => $trust->profile?->tax_number,
				'principal_email' => $trust->profile?->principal_email,
				'principal_number' => $trust->profile?->principal_number,
				'registration_date' => $trust->profile?->registration_date,
				'physical_address_1' => $trust->profile?->physical_address_1,
				'physical_address_2' => $trust->profile?->physical_address_2,
				'physical_city' => $trust->profile?->physical_city,
				'physical_province' => $trust->profile?->physical_province,
				'physical_postal_code' => $trust->profile?->physical_postal_code,
				'master_of_high_court_office' => $trust->profile?->master_of_high_court_office,
				'pos_address_1' => $trust->profile?->pos_address_1,
				'pos_address_2' => $trust->profile?->pos_address_2,
				'pos_city' => $trust->profile?->pos_city,
				'pos_province' => $trust->profile?->pos_province,
				'pos_postal_code' => $trust->profile?->pos_postal_code,
				'pos_country_id' => $trust->profile?->pos_country_id,
				'bank_details' => $trust->profile?->bank_details,
				'notes' => $trust->profile?->notes,
			]);
	}

	/**
	 * Test cannot access trust without permission
	 */
	public function test_cannot_access_trust_without_permission(): void
	{
		/** @var User $user */
		$user = User::factory()->create();
		$trust = Trust::factory()->create();

		// Don't create UserTrust relationship

		$this->actingAs($user)->get(sprintf('%s/%d', $this->baseUrl, $trust->id))
			->assertStatus(Response::HTTP_FORBIDDEN);
	}

	/**
	 * Test can update trust
	 */
	public function test_can_update_trust(): void
	{
		/** @var User $user */
		$user = User::factory()->create();
		$trust = Trust::factory()->withProfile()->create();

		UserTrust::factory()->create([
			'user_id' => $user->id,
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		$updateData = [
			'trust_name' => 'Updated Trust Name',
			'phone' => '**********',
			'email' => '<EMAIL>',
			'physical_address_1' => 'Updated Address',
			'physical_city' => 'Updated City',
		];

		$response = $this->actingAs($user)->put(sprintf('%s/%d', $this->baseUrl, $trust->id), $updateData);

		$response->assertStatus(Response::HTTP_OK)
			->assertJsonFragment([
				'message' => 'Trust updated successfully.'
			]);

		$trust->refresh();
		$this->assertEquals($updateData['trust_name'], $trust->trust_name);
		$this->assertEquals($updateData['phone'], $trust->phone);
		$this->assertEquals($updateData['email'], $trust->email);
	}

	/**
	 * Test can soft delete trust
	 */
	public function test_can_soft_delete_trust(): void
	{
		/** @var User $user */
		$user = User::factory()->create();
		$trust = Trust::factory()->withProfile()->create();

		UserTrust::factory()->create([
			'user_id' => $user->id,
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		// Create related trustees and beneficiaries
		$trustee = Trusteeship::factory()->create(['trust_id' => $trust->id]);
		$beneficiary = Beneficiary::factory()->create(['trust_id' => $trust->id]);

		$response = $this->actingAs($user)->delete(sprintf('%s/%d', $this->baseUrl, $trust->id));

		$response->assertStatus(Response::HTTP_NO_CONTENT);

		// Assert soft delete
		$trust->refresh();
		$this->assertEquals(StatusEnum::DELETED->value, $trust->status_id);

		$trustee->refresh();
		$this->assertEquals(StatusEnum::REMOVED->value, $trustee->status_id);

		$beneficiary->refresh();
		$this->assertEquals(StatusEnum::REMOVED->value, $beneficiary->status_id);
	}

	/**
	 * Show all active trust history
	 */
	public function test_can_return_active_trust_history(): void
	{
		// Create a trust using factory
		/** @var User $user */
		$user = User::factory()->create();
		$trust = Trust::factory()->forUser($user)->create();

		// Create active trust histories using factory
		$activeHistories = TrustHistory::factory()->count(3)->create([
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE,
		]);

		// Create inactive trust histories using factory
		TrustHistory::factory()->count(2)->create([
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::SUSPENDED,
		]);

		// Call the controller method
		$response = $this->actingAs($user)->json('GET', route('trust.history', ['trustId' => $trust->id]));

		// Assert that the response is successful
		$response->assertStatus(ResponseAlias::HTTP_OK);

		// Assert the structure and data of the response
		$response->assertJsonCount(3)
			->assertJsonStructure([
				'*' => ['description', 'change_date', 'trust_id']
			]);

		// Assert the response data matches the active histories
		$activeHistories->each(function ($history) use ($response) {
			$response->assertJsonFragment([
				'description' => $history->description,
				'change_date' => $history->change_date->format('Y-m-d'),
				'trust_id' => $history->trust_id,
			]);
		});
	}

	/**
	 * Create trust, trustees and beneficiaries
	 */
	public function test_can_create_trust_successfully(): void
	{
		Individual::factory()->create([
			'id_number' => '9511226118088',
		]);
		$faker = \Faker\Factory::create();
		// Mocking request data
		$requestData = [
			'trust' => [
				'trust_name' => $faker->company,
				'trust_number' => $faker->numerify('######'),
				'trust_master_office_id' => $faker->numberBetween(1, 17),
				'financial_year_end' => $faker->month(),
				'phone' => $faker->phoneNumber,
				'email' => $faker->email,
				'tax_number' => $faker->numerify('#############'),
				'physical_address_1' => $faker->streetAddress,
				'physical_address_2' => $faker->address,
				'physical_city' => $faker->city,
				'physical_province' => $faker->state,
				'physical_postal_code' => $faker->postcode,
				'physical_country' => $faker->country(),
				'physical_country_id' => $faker->numberBetween(1, 250),
				'pos_address_1' => $faker->streetAddress,
				'pos_address_2' => $faker->address,
				'pos_city' => $faker->city,
				'pos_province' => $faker->state,
				'pos_postal_code' => $faker->postcode,
				'pos_country' => $faker->numberBetween(1, 250),
			],
			'trustees' => [[
				'status_id' => 1,
				'entity_id' => 1,
				'entity_type' => 1,
				'date_appointed' => $faker->date(),
				'date_resigned' => $faker->date(),
				'initials' => $faker->randomLetter . $faker->randomLetter,
				'first_name' => $faker->firstName,
				'second_name' => $faker->firstName,
				'surname' => $faker->lastName,
				'date_of_birth' => $faker->date(),
				'citizen' => $faker->boolean,
				'disability' => $faker->boolean,
				'id_number' => $faker->numerify('#############'),
				'email' => $faker->email,
				'phone' => $faker->phoneNumber,
				'res_address_1' => $faker->streetAddress,
				'res_address_2' => $faker->address,
				'res_city' => $faker->city,
				'res_province' => $faker->state,
				'country_id' => $faker->numberBetween(1, 250),
				'role' => 'TRUSTEE',
				'res_postal_code' => $faker->postcode,
				'res_country_id' => $faker->numberBetween(1, 250),
				'pos_country_id' => $faker->numberBetween(1, 250),
				'pos_address_1' => $faker->streetAddress,
				'pos_address_2' => $faker->address,
				'pos_city' => $faker->city,
				'pos_province' => $faker->state,
				'pos_postal_code' => $faker->postcode,
			]],
			'beneficiaries' => [[
				'status_id' => 1,
				'beneficial_interest' => $faker->numberBetween(1, 100),
				'email' => $faker->email,
				'phone' => $faker->phoneNumber,
				'res_address_1' => $faker->streetAddress,
				'res_address_2' => $faker->address,
				'res_city' => $faker->city,
				'role' => 'BENEFICIARY',
				'res_province' => $faker->state,
				'res_postal_code' => $faker->postcode,
				'res_country_id' => $faker->numberBetween(1, 250),
				'pos_country_id' => $faker->numberBetween(1, 250),
				'pos_address_1' => $faker->streetAddress,
				'pos_address_2' => $faker->address,
				'pos_city' => $faker->city,
				'pos_province' => $faker->state,
				'pos_postal_code' => $faker->postcode,
				'initials' => $faker->randomLetter . $faker->randomLetter,
			]],
		];

		// Mocking the user
		/** @var User $user */
		$user = User::factory()->create();
		Trust::factory()->forUser($user)->create();

		$response = $this->actingAs($user)->json('POST', route('trust.add'), $requestData);

		$response->assertStatus(ResponseAlias::HTTP_CREATED);
	}

	/**
	 * Test can get trustees for a trust
	 */
	public function test_can_get_trustees(): void
	{
		/** @var User $user */
		$user = User::factory()->create();
		$trust = Trust::factory()->create();

		UserTrust::factory()->create([
			'user_id' => $user->id,
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		// Create trustees
		$trustees = Trusteeship::factory()->count(3)->create([
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		// Create a removed trustee (should not appear)
		Trusteeship::factory()->create([
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::REMOVED
		]);

		$response = $this->actingAs($user)->get(sprintf('%s/%d/trustees', $this->baseUrl, $trust->id));

		$response->assertStatus(Response::HTTP_OK)
			->assertJsonCount(3, 'data');
	}

	/**
	 * Test can get single trustee
	 */
	public function test_can_get_single_trustee(): void
	{
		/** @var User $user */
		$user = User::factory()->create();
		$trust = Trust::factory()->create();
		$trustee = Trusteeship::factory()->create([
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		UserTrust::factory()->create([
			'user_id' => $user->id,
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		$response = $this->actingAs($user)->get(sprintf('%s/%d/trustees/%d', $this->baseUrl, $trust->id, $trustee->id));

		$response->assertStatus(Response::HTTP_OK)
			->assertJsonFragment([
				'id' => $trustee->id,
				'email' => $trustee->email,
				'phone' => $trustee->phone,
			])
			->assertJsonStructure([
				'trustee' => [
					'id',
					'individual_id',
					'trust_id',
					'entity_id',
					'entity_type',
					'status_id',
					'status',
					'date_appointed',
					'date_resigned',
					'initials',
					'email',
					'phone',
					'role',
					'individual'
				]
			]);
	}

	/**
	 * Test cannot get removed trustee
	 */
	public function test_cannot_get_removed_trustee(): void
	{
		/** @var User $user */
		$user = User::factory()->create();
		$trust = Trust::factory()->create();
		$trustee = Trusteeship::factory()->create([
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::REMOVED
		]);

		UserTrust::factory()->create([
			'user_id' => $user->id,
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		$response = $this->actingAs($user)->get(sprintf('%s/%d/trustees/%d', $this->baseUrl, $trust->id, $trustee->id));

		$response->assertStatus(Response::HTTP_NOT_FOUND);
	}

	/**
	 * Test can get beneficiaries for a trust
	 */
	public function test_can_get_beneficiaries(): void
	{
		/** @var User $user */
		$user = User::factory()->create();
		$trust = Trust::factory()->create();

		UserTrust::factory()->create([
			'user_id' => $user->id,
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		// Create beneficiaries
		$beneficiaries = Beneficiary::factory()->count(2)->create([
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		// Create a removed beneficiary (should not appear)
		Beneficiary::factory()->create([
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::REMOVED
		]);

		$response = $this->actingAs($user)->get(sprintf('%s/%d/beneficiaries', $this->baseUrl, $trust->id));

		$response->assertStatus(Response::HTTP_OK)
			->assertJsonCount(2, 'data');
	}

	/**
	 * Test can get single beneficiary
	 */
	public function test_can_get_single_beneficiary(): void
	{
		/** @var User $user */
		$user = User::factory()->create();
		$trust = Trust::factory()->create();
		$beneficiary = Beneficiary::factory()->create([
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		UserTrust::factory()->create([
			'user_id' => $user->id,
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		$response = $this->actingAs($user)->get(sprintf('%s/%d/beneficiaries/%d', $this->baseUrl, $trust->id, $beneficiary->id));

		$response->assertStatus(Response::HTTP_OK)
			->assertJsonFragment([
				'id' => $beneficiary->id,
				'email' => $beneficiary->email,
			])
			->assertJsonStructure([
				'beneficiary' => [
					'id',
					'status_id',
					'status',
					'trust_id',
					'entity_id',
					'entity_type',
					'beneficial_interest',
					'email',
					'phone'
				]
			]);
	}

	/**
	 * Test cannot get removed beneficiary
	 */
	public function test_cannot_get_removed_beneficiary(): void
	{
		/** @var User $user */
		$user = User::factory()->create();
		$trust = Trust::factory()->create();
		$beneficiary = Beneficiary::factory()->create([
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::REMOVED
		]);

		UserTrust::factory()->create([
			'user_id' => $user->id,
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		$response = $this->actingAs($user)->get(sprintf('%s/%d/beneficiaries/%d', $this->baseUrl, $trust->id, $beneficiary->id));

		$response->assertStatus(Response::HTTP_NOT_FOUND);
	}

	/**
	 * Create beneficiaries
	 */
	public function test_can_add_beneficiaries(): void
	{
		$faker = \Faker\Factory::create();
		/** @var User $user */
		$user = User::factory()->create();
		$trust = Trust::factory()->create();

		UserTrust::factory()->create([
			'user_id' => $user->id,
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		$requestData = [
			'trust_id' => $trust->id,
			'entity_id' => 1,
			'entity_type' => 1,
			'status_id' => 1,
			'initials' => $faker->randomLetter() . $faker->randomLetter(),
			'beneficial_interest' => 100.00,
			'email' => $faker->email,
			'phone' => $faker->phoneNumber,
			'res_address_1' => $faker->address,
			'res_address_2' => $faker->address,
			'res_city' => $faker->city,
			'res_province' => $faker->state,
			'res_postal_code' => $faker->postcode,
			'res_country_id' => $faker->numberBetween(1, 250),
			'role' => 'BENEFICIARY',
			'pos_address_1' => $faker->address,
			'pos_address_2' => $faker->address,
			'pos_city' => $faker->city,
			'pos_province' => $faker->state,
			'pos_postal_code' => $faker->postcode,
			'pos_country_id' => $faker->numberBetween(1, 250),
		];

		$response = $this->actingAs($user)->Json('POST', route('beneficiary.add'), $requestData);
		// Assert response is either 200 or 201
		$response->assertStatus(Response::HTTP_CREATED)
			->assertJsonFragment([
				'success' => true,
				'message' => 'Beneficiary added successfully',
			]);
	}

	/**
	 * Test can update beneficiary
	 */
	public function test_can_update_beneficiary(): void
	{
		/** @var User $user */
		$user = User::factory()->create();
		$trust = Trust::factory()->create();
		$beneficiary = Beneficiary::factory()->create(['trust_id' => $trust->id]);

		UserTrust::factory()->create([
			'user_id' => $user->id,
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		$updateData = [
			'beneficial_interest' => 75.50,
			'email' => '<EMAIL>',
			'phone' => '0987654321',
		];

		$response = $this->actingAs($user)->put(
			sprintf('%s/%d/beneficiaries/%d', $this->baseUrl, $trust->id, $beneficiary->id),
			$updateData
		);

		$response->assertStatus(Response::HTTP_OK)
			->assertJsonFragment([
				'message' => 'Beneficiary updated successfully.'
			]);

		$beneficiary->refresh();
		$this->assertEquals($updateData['beneficial_interest'], $beneficiary->beneficial_interest);
		$this->assertEquals($updateData['email'], $beneficiary->email);
	}

	/**
	 * Test can soft delete beneficiary
	 */
	public function test_can_soft_delete_beneficiary(): void
	{
		/** @var User $user */
		$user = User::factory()->create();
		$trust = Trust::factory()->create();
		$beneficiary = Beneficiary::factory()->create(['trust_id' => $trust->id]);

		UserTrust::factory()->create([
			'user_id' => $user->id,
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		$response = $this->actingAs($user)->delete(
			sprintf('%s/%d/beneficiaries/%d', $this->baseUrl, $trust->id, $beneficiary->id)
		);

		$response->assertStatus(Response::HTTP_NO_CONTENT);

		$beneficiary->refresh();
		$this->assertEquals(StatusEnum::REMOVED->value, $beneficiary->status_id);
	}

	/**
	 * Create trustee
	 */
	public function test_can_add_trustees(): void
	{
		$faker = \Faker\Factory::create();
		/** @var User $user */
		$user = User::factory()->create();
		$trust = Trust::factory()->withProfile()->create();
		$trusteeship = Trusteeship::factory(['trust_id' =>  $trust->id])->create();

		UserTrust::factory()->create([
			'user_id' => $user->id,
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		$requestData = [
			'status_id' => 1,
			'trust_id' => $trust->id,
			'individual_id' => $trusteeship->individual_id,
			'entity_id' => $faker->numberBetween(1, 100),
			'entity_type' => 1,
			'date_appointed' => $faker->date(),
			'date_resigned' => $faker->date(),
			'initials' => $faker->randomLetter . $faker->randomLetter,
			'email' => $faker->email,
			'phone' => $faker->phoneNumber,
			'res_address_1' => $faker->streetAddress,
			'res_address_2' => $faker->address,
			'res_city' => $faker->city,
			'res_province' => $faker->state,
			'role' => 'TRUSTEE',
			'res_postal_code' => $faker->postcode,
			'res_country_id' => $faker->numberBetween(1, 250),
			'pos_country_id' => $faker->numberBetween(1, 250),
			'pos_address_1' => $faker->streetAddress,
			'pos_address_2' => $faker->address,
			'pos_city' => $faker->city,
			'pos_province' => $faker->state,
			'pos_postal_code' => $faker->postcode,
		];

		$response = $this->actingAs($user)->Json(
			'POST',
			route('trustee.add', $trust->id),
			$requestData
		);

		// Assert response is either 200 or 201
		$response->assertStatus(Response::HTTP_CREATED)
			->assertJsonFragment(['message' => 'Trustee created successfully.']);
	}

	/**
	 * Test can update trustee
	 */
	public function test_can_update_trustee(): void
	{
		/** @var User $user */
		$user = User::factory()->create();
		$trust = Trust::factory()->create();
		$trustee = Trusteeship::factory()->create(['trust_id' => $trust->id]);

		UserTrust::factory()->create([
			'user_id' => $user->id,
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		$updateData = [
			'email' => '<EMAIL>',
			'phone' => '0987654321',
			'role' => 'CHAIRMAN',
		];

		$response = $this->actingAs($user)->put(
			sprintf('%s/%d/trustees/%d', $this->baseUrl, $trust->id, $trustee->id),
			$updateData
		);

		$response->assertStatus(Response::HTTP_OK)
			->assertJsonFragment([
				'message' => 'Trustee updated successfully.'
			]);

		$trustee->refresh();
		$this->assertEquals($updateData['email'], $trustee->email);
		$this->assertEquals($updateData['phone'], $trustee->phone);
		$this->assertEquals($updateData['role'], $trustee->role);
	}

	/**
	 * Test can soft delete trustee
	 */
	public function test_can_soft_delete_trustee(): void
	{
		/** @var User $user */
		$user = User::factory()->create();
		$trust = Trust::factory()->create();
		$trustee = Trusteeship::factory()->create(['trust_id' => $trust->id]);

		UserTrust::factory()->create([
			'user_id' => $user->id,
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		$response = $this->actingAs($user)->delete(
			sprintf('%s/%d/trustees/%d', $this->baseUrl, $trust->id, $trustee->id)
		);

		$response->assertStatus(Response::HTTP_NO_CONTENT);

		$trustee->refresh();
		$this->assertEquals(StatusEnum::REMOVED->value, $trustee->status_id);
	}

	/**
	 * Test authorization - user cannot access trust they don't own
	 */
	public function test_cannot_access_trust_without_authorization(): void
	{
		/** @var User $user */
		$user = User::factory()->create();
		$otherUser = User::factory()->create();
		$trust = Trust::factory()->create();

		// Create UserTrust for other user, not the current user
		UserTrust::factory()->create([
			'user_id' => $otherUser->id,
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		// Test various endpoints that should be forbidden
		$this->actingAs($user)->get(sprintf('%s/%d', $this->baseUrl, $trust->id))
			->assertStatus(Response::HTTP_FORBIDDEN);

		$this->actingAs($user)->put(sprintf('%s/%d', $this->baseUrl, $trust->id), [])
			->assertStatus(Response::HTTP_FORBIDDEN);

		$this->actingAs($user)->delete(sprintf('%s/%d', $this->baseUrl, $trust->id))
			->assertStatus(Response::HTTP_FORBIDDEN);

		$this->actingAs($user)->get(sprintf('%s/%d/trustees', $this->baseUrl, $trust->id))
			->assertStatus(Response::HTTP_FORBIDDEN);

		$this->actingAs($user)->get(sprintf('%s/%d/beneficiaries', $this->baseUrl, $trust->id))
			->assertStatus(Response::HTTP_FORBIDDEN);
	}

	/**
	 * Test support user can access any trust
	 */
	public function test_support_user_can_access_any_trust(): void
	{
		$supportUser = User::factory()->create(['user_type_id' => \App\Enum\UserRoleEnum::SUPPORT->value]);
		$trust = Trust::factory()->create();

		// No UserTrust relationship needed for support users

		$this->actingAs($supportUser)->get(sprintf('%s/%d', $this->baseUrl, $trust->id))
			->assertStatus(Response::HTTP_OK);

		$this->actingAs($supportUser)->get(sprintf('%s/%d/trustees', $this->baseUrl, $trust->id))
			->assertStatus(Response::HTTP_OK);

		$this->actingAs($supportUser)->get(sprintf('%s/%d/beneficiaries', $this->baseUrl, $trust->id))
			->assertStatus(Response::HTTP_OK);
	}

	/**
	 * Test validation errors for trust update
	 */
	public function test_trust_update_validation_errors(): void
	{
		/** @var User $user */
		$user = User::factory()->create();
		$trust = Trust::factory()->create();

		UserTrust::factory()->create([
			'user_id' => $user->id,
			'trust_id' => $trust->id,
			'status_id' => StatusEnum::ACTIVE
		]);

		$invalidData = [
			'email' => 'invalid-email',
			'phone' => str_repeat('1', 100), // Too long
		];

		$response = $this->actingAs($user)->putJson(sprintf('%s/%d', $this->baseUrl, $trust->id), $invalidData);

		$response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
	}

	/**
	 * Test that soft deleted trusts don't appear in listings
	 */
	public function test_soft_deleted_trusts_not_in_listings(): void
	{
		/** @var User $user */
		$user = User::factory()->create();

		// Create active trust
		$activeTrust = Trust::factory()->forUser($user)->create(['status_id' => StatusEnum::ACTIVE->value]);

		// Create deleted trust
		$deletedTrust = Trust::factory()->forUser($user)->create(['status_id' => StatusEnum::DELETED->value]);

		$response = $this->actingAs($user)->get($this->baseUrl);

		$response->assertStatus(Response::HTTP_OK)
			->assertJsonFragment(['id' => $activeTrust->id])
			->assertJsonMissing(['id' => $deletedTrust->id]);
	}
}
