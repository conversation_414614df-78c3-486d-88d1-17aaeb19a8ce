<?php

namespace Tests\Feature\Http\Controllers;

use App\Enum\AuditTypeEnum;
use App\Enum\PermissionEnum;
use App\Enum\ResolutionTypeEnum;
use App\Enum\ShareholderTypeEnum;
use App\Enum\ShareTransactionEnum;
use App\Enum\StatusEnum;
use App\Models\Account;
use App\Models\AccountCompany;
use App\Models\AccountUser;
use App\Models\Company;
use App\Models\Individual;
use App\Models\ShareClass;
use App\Models\ShareTransaction;
use App\Models\Shareholding;
use App\Models\User;
use App\Models\UserCompany;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Symfony\Component\HttpFoundation\Response;

class ShareTransactionControllerTest extends TestCase
{
	use RefreshDatabase;

	private User $user;
	private Company $company;
	private Account $account;
	private ShareClass $shareClass;

	protected function setUp(): void
	{
		parent::setUp();

		$this->user = User::factory()->create();
		$this->company = Company::factory()->withProfile()->create();
		$this->account = Account::factory()->create();

		// Create account user relationship
		AccountUser::factory()->create([
			'user_id' => $this->user->id,
			'account_id' => $this->account->id,
			'status_id' => StatusEnum::ACTIVE->value,
		]);

		// Create company ownership
		AccountCompany::factory()->create([
			'account_id' => $this->account->id,
			'company_id' => $this->company->id,
			'status_id' => StatusEnum::ACTIVE->value,
		]);

		// Create user company access with edit permission
		UserCompany::factory()->create([
			'user_id' => $this->user->id,
			'company_id' => $this->company->id,
			'permission_id' => PermissionEnum::EDIT->value,
			'status_id' => StatusEnum::ACTIVE->value,
		]);

		// Create share class
		$this->shareClass = ShareClass::factory()->create([
			'company_id' => $this->company->id,
			'total_issued' => 1000,
			'total_authorised' => 2000,
		]);
	}

	public function test_clear_share_register_successfully_removes_all_data()
	{
		// Create share transactions
		$shareTransaction1 = ShareTransaction::factory()->create([
			'share_class_id' => $this->shareClass->id,
		]);

		$shareTransaction2 = ShareTransaction::factory()->create([
			'share_class_id' => $this->shareClass->id,
		]);

		$shareTransaction3 = ShareTransaction::factory()->create([
			'share_class_id' => $this->shareClass->id,
		]);

		// Create shareholdings
		Shareholding::factory()->create([
			'share_transaction_id' => $shareTransaction1->id,
		]);

		Shareholding::factory()->create([
			'share_transaction_id' => $shareTransaction2->id,
		]);

		Shareholding::factory()->create([
			'share_transaction_id' => $shareTransaction3->id,
		]);



		$response = $this->actingAs($this->user)
			->postJson('/api/v1/share/transaction/clear-share-register', [
				'company_id' => $this->company->id,
			]);

		$response->assertStatus(Response::HTTP_OK)
			->assertJson([
				'success' => true,
			]);

		// Verify data is actually deleted
		$this->assertDatabaseMissing('share_transaction', [
			'share_class_id' => $this->shareClass->id,
		]);

		$this->assertDatabaseMissing('shareholding', [
			'share_transaction_id' => $shareTransaction1->id,
			'share_transaction_id' => $shareTransaction2->id,
			'share_transaction_id' => $shareTransaction3->id,
		]);

		$this->assertDatabaseMissing('share_class', [
			'id' => $this->shareClass->id,
		]);
	}

	public function test_clear_share_register_handles_empty_share_classes()
	{
		// Remove existing share class
		$this->shareClass->delete();

		$response = $this->actingAs($this->user)
			->postJson('/api/v1/share/transaction/clear-share-register', [
				'company_id' => $this->company->id,
			]);

		$response->assertStatus(Response::HTTP_OK)
			->assertJson([
				'success' => true,
			]);
	}

	public function test_clear_share_register_creates_audit_log()
	{
		ShareTransaction::factory()->create([
			'share_class_id' => $this->shareClass->id,
			'company_id' => $this->company->id,
		]);

		$this->actingAs($this->user)
			->postJson('/api/v1/share/transaction/clear-share-register', [
				'company_id' => $this->company->id,
			]);

		$this->assertDatabaseHas('audit_log', [
			'user_id' => $this->user->id,
			'company_id' => $this->company->id,
			'audit_type_id' => AuditTypeEnum::SHARE_TRANSACTION_DELETED->value,
		]);
	}

	public function test_user_id_is_populated_when_creating_share_transaction()
	{
		// Create a shareholding first
		$shareholding = Shareholding::factory()->create();

		// Act as authenticated user and create a share transaction
		$this->actingAs($this->user);

		$shareTransaction = ShareTransaction::create([
			'share_class_id' => $this->shareClass->id,
			'type_id' => ShareTransactionEnum::SHARE_ALLOT->value,
			'total_shares' => 100,
			'price' => 1000,
			'transaction_date' => now(),
			'to_shareholding_id' => $shareholding->id,
		]);

		// Assert the user_id was automatically populated
		$this->assertNotNull($shareTransaction->user_id);
		$this->assertEquals($this->user->id, $shareTransaction->user_id);

		// Verify in database
		$this->assertDatabaseHas('share_transaction', [
			'id' => $shareTransaction->id,
			'user_id' => $this->user->id,
		]);
	}

	public function test_user_id_is_populated_when_allotting_shares()
	{
		// Act as authenticated user
		$this->actingAs($this->user);

		// Count transactions before
		$transactionCountBefore = ShareTransaction::count();

		// Create an individual shareholder
		$individual = Individual::factory()->create();

		// Create a previous shareholding
		$prevShareholding = Shareholding::factory()->create([
			'individual_id' => $individual->id,
		]);

		// Set up request data that mimics the API call
		request()->merge([
			'share_class_id' => $this->shareClass->id,
			'total_shares' => 100,
			'price' => 1000,
			'date_issued' => now()->format('Y-m-d'),
			'certificate_no' => 1,
			'prev_shareholding_id' => $prevShareholding->id,
		]);

		// Call the service method directly (mimicking what the API does)
		$shareholdingService = app(\App\Services\ShareholdingService::class);
		$shareholdingService->allot();

		// Get the newly created share transaction
		$shareTransaction = ShareTransaction::where('share_class_id', $this->shareClass->id)
			->where('type_id', ShareTransactionEnum::SHARE_ALLOT->value)
			->where('total_shares', 100)
			->latest('id')
			->first();

		// Assert the share transaction was created
		$this->assertNotNull($shareTransaction);
		$this->assertEquals($transactionCountBefore + 1, ShareTransaction::count());

		// Assert user_id is populated with the authenticated user
		$this->assertNotNull($shareTransaction->user_id);
		$this->assertEquals($this->user->id, $shareTransaction->user_id);
	}

	public function test_user_id_is_populated_when_transferring_shares()
	{
		// Act as authenticated user
		$this->actingAs($this->user);

		// Count transactions before
		$transactionCountBefore = ShareTransaction::count();

		// Create from shareholder and shareholding
		$fromIndividual = Individual::factory()->create();
		$fromShareholding = Shareholding::factory()->create([
			'individual_id' => $fromIndividual->id,
			'holding' => 200,
			'certificate_no' => 100,
		]);

		// Create to shareholder
		$toIndividual = Individual::factory()->create();
		$toShareholding = Shareholding::factory()->create([
			'individual_id' => $toIndividual->id,
		]);

		// Set up request data that mimics the transfer API call
		request()->merge([
			'share_class_id' => $this->shareClass->id,
			'from_shareholding_id' => $fromShareholding->id,
			'prev_shareholding_id' => $toShareholding->id,
			'total_shares' => 100,
			'price' => 5000,
			'date_transferred' => now()->format('Y-m-d'),
			'new_certificate_no' => 101,
			'balance_certificate_no' => 100,
			'balance_holding' => 100,
			'company_id' => $this->company->id,
		]);

		// Call the service method directly (mimicking what the API does)
		$shareholdingService = app(\App\Services\ShareholdingService::class);
		$shareholdingService->transfer();

		// Get the newly created share transaction
		$shareTransaction = ShareTransaction::where('share_class_id', $this->shareClass->id)
			->where('type_id', ShareTransactionEnum::SHARE_TRANSFER->value)
			->where('total_shares', 100)
			->where('from_shareholding_id', $fromShareholding->id)
			->latest('id')
			->first();

		// Assert the share transaction was created
		$this->assertNotNull($shareTransaction);
		$this->assertGreaterThanOrEqual($transactionCountBefore + 1, ShareTransaction::count());

		// Assert user_id is populated with the authenticated user
		$this->assertNotNull($shareTransaction->user_id);
		$this->assertEquals($this->user->id, $shareTransaction->user_id);
	}

	public function test_user_id_is_populated_when_creating_share_class()
	{
		// Act as authenticated user
		$this->actingAs($this->user);

		// Count transactions before
		$transactionCountBefore = ShareTransaction::count();

		// Prepare request for share class creation
		$request = new \Illuminate\Http\Request([
			'company_id' => $this->company->id,
			'name' => 'Class B Shares',
			'shareclass_type_id' => 6,
			'total_authorised' => 10000,
			'description' => 'Ordinary Shares',
			'total_issued' => 0,
			'date' => now()->format('Y-m-d'),
		]);

		// Call the service method directly
		$shareClassService = app(\App\Services\ShareClassService::class);
		$shareClassService->add($request);

		// Get the newly created share transaction
		$shareTransaction = ShareTransaction::where('type_id', ShareTransactionEnum::SHARE_CLASS_CREATE->value)
			->latest('id')
			->first();

		// Assert the share transaction was created
		$this->assertNotNull($shareTransaction);
		$this->assertGreaterThanOrEqual($transactionCountBefore + 1, ShareTransaction::count());

		// Assert user_id is populated with the authenticated user
		$this->assertNotNull($shareTransaction->user_id);
		$this->assertEquals($this->user->id, $shareTransaction->user_id);
	}

	public function test_user_id_is_populated_when_buying_back_shares()
	{
		// Act as authenticated user
		$this->actingAs($this->user);

		// Count transactions before
		$transactionCountBefore = ShareTransaction::count();

		// Create shareholder and shareholding
		$individual = Individual::factory()->create();
		$shareholding = Shareholding::factory()->create([
			'individual_id' => $individual->id,
			'holding' => 500,
			'certificate_no' => 200,
		]);

		// Set up request data for buyback
		request()->merge([
			'share_class_id' => $this->shareClass->id,
			'shareholding_id' => $shareholding->id,
			'type_id' => ShareTransactionEnum::SHARE_BUYBACK->value,
			'total_shares' => 500,
			'price' => 10000,
			'date_purchased' => now()->format('Y-m-d'),
			'resolution_type' => 'special',
			'balance_holding' => 0,
		]);

		// Call the service method directly
		$shareholdingService = app(\App\Services\ShareholdingService::class);
		$shareholdingService->purchase();

		// Get the newly created share transaction
		$shareTransaction = ShareTransaction::where('share_class_id', $this->shareClass->id)
			->where('type_id', ShareTransactionEnum::SHARE_BUYBACK->value)
			->where('total_shares', 500)
			->where('from_shareholding_id', $shareholding->id)
			->latest('id')
			->first();

		// Assert the share transaction was created
		$this->assertNotNull($shareTransaction);

		// Assert user_id is populated with the authenticated user
		$this->assertNotNull($shareTransaction->user_id);
		$this->assertEquals($this->user->id, $shareTransaction->user_id);
	}

	public function test_user_id_is_populated_when_redeeming_shares()
	{
		// Act as authenticated user
		$this->actingAs($this->user);

		// Count transactions before
		$transactionCountBefore = ShareTransaction::count();

		// Create shareholder and shareholding
		$individual = Individual::factory()->create();
		$shareholding = Shareholding::factory()->create([
			'individual_id' => $individual->id,
			'holding' => 300,
			'certificate_no' => 300,
		]);

		// Set up request data for redemption
		request()->merge([
			'share_class_id' => $this->shareClass->id,
			'shareholding_id' => $shareholding->id,
			'type_id' => ShareTransactionEnum::SHARE_REDEEM->value,
			'total_shares' => 300,
			'price' => 6000,
			'date_purchased' => now()->format('Y-m-d'),
			'balance_holding' => 0,
		]);

		// Call the service method directly
		$shareholdingService = app(\App\Services\ShareholdingService::class);
		$shareholdingService->purchase();

		// Get the newly created share transaction
		$shareTransaction = ShareTransaction::where('share_class_id', $this->shareClass->id)
			->where('type_id', ShareTransactionEnum::SHARE_REDEEM->value)
			->where('total_shares', 300)
			->where('from_shareholding_id', $shareholding->id)
			->latest('id')
			->first();

		// Assert the share transaction was created
		$this->assertNotNull($shareTransaction);

		// Assert user_id is populated with the authenticated user
		$this->assertNotNull($shareTransaction->user_id);
		$this->assertEquals($this->user->id, $shareTransaction->user_id);
	}

	public function test_user_id_is_populated_when_converting_shares()
	{
		// Act as authenticated user
		$this->actingAs($this->user);

		// Create a "from" share class with existing shareholding
		$fromShareClass = ShareClass::factory()->create([
			'company_id' => $this->company->id,
			'name' => 'Class A',
		]);

		// Create a "to" share class
		$toShareClass = ShareClass::factory()->create([
			'company_id' => $this->company->id,
			'name' => 'Class B',
		]);

		// Create shareholding
		$individual = Individual::factory()->create();
		$shareholding = Shareholding::factory()->create([
			'individual_id' => $individual->id,
			'holding' => 1000,
			'status_id' => StatusEnum::CURRENT->value,
		]);

		// Create share transaction for the from shareholding
		$fromShareTransaction = ShareTransaction::factory()->create([
			'share_class_id' => $fromShareClass->id,
			'type_id' => ShareTransactionEnum::SHARE_ALLOT->value,
			'to_shareholding_id' => $shareholding->id,
		]);

		$shareholding->share_transaction_id = $fromShareTransaction->id;
		$shareholding->save();

		// Set up request for share conversion
		$request = new \Illuminate\Http\Request([
			'company_id' => $this->company->id,
			'date_converted' => now()->format('Y-m-d'),
		]);

		// Count transactions before
		$transactionCountBefore = ShareTransaction::count();

		// Call the service method directly
		$shareClassService = app(\App\Services\ShareClassService::class);
		$shareClassService->removeFromShareholding($request, $fromShareClass, $toShareClass, now()->format('Y-m-d'));

		// Get the newly created convert cancel transaction
		$cancelTransaction = ShareTransaction::where('share_class_id', $fromShareClass->id)
			->where('type_id', ShareTransactionEnum::SHARE_CONVERT_CANCEL->value)
			->latest('id')
			->first();

		// Get the newly created convert create transaction
		$createTransaction = ShareTransaction::where('share_class_id', $toShareClass->id)
			->where('type_id', ShareTransactionEnum::SHARE_CONVERT_CREATE->value)
			->latest('id')
			->first();

		// Assert both transactions were created
		$this->assertNotNull($cancelTransaction);
		$this->assertNotNull($createTransaction);

		// Assert user_id is populated on cancel transaction
		$this->assertNotNull($cancelTransaction->user_id);
		$this->assertEquals($this->user->id, $cancelTransaction->user_id);

		// Assert user_id is populated on create transaction
		$this->assertNotNull($createTransaction->user_id);
		$this->assertEquals($this->user->id, $createTransaction->user_id);
	}

	public function test_user_id_not_set_when_no_authenticated_user()
	{
		// Create a shareholding first
		$shareholding = Shareholding::factory()->create();

		// Create share transaction without authentication
		$shareTransaction = ShareTransaction::create([
			'share_class_id' => $this->shareClass->id,
			'type_id' => ShareTransactionEnum::SHARE_ALLOT->value,
			'total_shares' => 100,
			'price' => 1000,
			'transaction_date' => now(),
			'to_shareholding_id' => $shareholding->id,
		]);

		// Assert user_id is null when no authenticated user
		$this->assertNull($shareTransaction->user_id);
	}
}
