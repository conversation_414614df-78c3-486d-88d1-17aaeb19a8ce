<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::table('share_transaction', function (Blueprint $table) {
			$table->unsignedInteger('user_id')->nullable()->after('id')->index('FK_sharetransaction__user');
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::table('share_transaction', function (Blueprint $table) {
			$table->dropIndex('FK_sharetransaction__user');
			$table->dropColumn('user_id');
		});
	}
};
