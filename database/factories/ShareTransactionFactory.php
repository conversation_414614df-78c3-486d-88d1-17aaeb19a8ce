<?php

namespace Database\Factories;

use App\Enum\ShareTransactionEnum;
use App\Models\Company;
use App\Models\ShareClass;
use App\Models\Shareholding;
use App\Models\ShareTransaction;
use App\Models\Status;
use App\Models\Type;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<ShareTransaction>
 */
class ShareTransactionFactory extends Factory
{
	/**
	 * Define the model's default state.
	 *
	 * @return array<string, mixed>
	 */
	public function definition(): array
	{
		return [
			'user_id' => null,
			'to_shareholding_id' => null,
			'from_shareholding_id' => null,
			'share_class_id' => ShareClass::factory(),
			'status_id' => Status::first()->id,
			'total_shares' => $this->faker->numberBetween(1, 1000000),
			'price' => $this->faker->randomFloat(2),
			'transaction_date' => $this->faker->date,
			'id_text' => $this->faker->uuid(),
			'type_id' => Type::whereIn('id', array_column(ShareTransactionEnum::cases(), 'value'))->first()->id,
		];
	}

	public function forAllotment(): ShareTransactionFactory|Factory
	{
		return $this->state(function (array $attributes) {
			return [
				'from_shareholding_id' => null,
				'type_id' => ShareTransactionEnum::SHARE_ALLOT,
			];
		});
	}

	public function forTransfer(): ShareTransactionFactory|Factory
	{
		return $this->state(function (array $attributes) {
			return [
				'type_id' => ShareTransactionEnum::SHARE_TRANSFER,
			];
		});
	}

	public function forShareClass(ShareClass $shareClass): ShareTransactionFactory|Factory
	{
		return $this->state(function (array $attributes) use ($shareClass) {
			return [
				'share_class_id' => $shareClass->id,
			];
		});
	}

	public function forShareholding(Shareholding $shareholding): ShareTransactionFactory|Factory
	{
		return $this->state(function (array $attributes) use ($shareholding) {
			return [
				'to_shareholding_id' => $shareholding->id,
				'total_shares' => $shareholding->holding,
				'transaction_date' => $shareholding->date_received,
				// 'share_class_id' => $shareholding->shareClass->id,
			];
		});
	}

	public function withToShareholding(?Shareholding $shareholding = null): ShareTransactionFactory|Factory
	{
		return $this->state(function (array $attributes) use ($shareholding) {
			return [
				'to_shareholding_id' => $shareholding ? $shareholding->id : Shareholding::factory(),
			];
		});
	}

	public function withFromShareholding(?Shareholding $shareholding = null): ShareTransactionFactory|Factory
	{
		return $this->state(function (array $attributes) use ($shareholding) {
			return [
				'from_shareholding_id' => $shareholding ? $shareholding->id : Shareholding::factory(),
			];
		});
	}

	public function withShareholdings(?Shareholding $toShareholding = null, ?Shareholding $fromShareholding = null): ShareTransactionFactory|Factory
	{
		return $this->state(function (array $attributes) use ($toShareholding, $fromShareholding) {
			return [
				'to_shareholding_id' => $toShareholding ? $toShareholding->id : Shareholding::factory(),
				'from_shareholding_id' => $fromShareholding ? $fromShareholding->id : Shareholding::factory(),
			];
		});
	}

	public function forUser(?User $user = null): ShareTransactionFactory|Factory
	{
		return $this->state(function (array $attributes) use ($user) {
			return [
				'user_id' => $user ? $user->id : User::factory(),
			];
		});
	}
}
