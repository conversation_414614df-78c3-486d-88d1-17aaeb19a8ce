<?php

namespace Database\Factories;

use App\Enum\StatusEnum;
use App\Models\Individual;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Beneficiary>
 */
class BeneficiaryFactory extends Factory
{
	/**
	 * Define the model's default state.
	 *
	 * @return array<string, mixed>
	 */
	public function definition()
	{
		return [
			'entity_id' => 1,
			'entity_type' => 'Beneficiary',
			'beneficial_interest' => 100,
			'initials' => 'N/A',
			'status_id' => StatusEnum::ACTIVE->value,
			'email' => $this->faker->email,
			'phone' => $this->faker->phoneNumber,
			'res_address_1' => $this->faker->address,
			'res_address_2' => $this->faker->address,
			'res_city' => $this->faker->city,
			'role' => 'Capitol Beneficiary',
			'date_appointed' => $this->faker->date,
			'res_province' => 'Eastern Cape',
			'res_postal_code' => $this->faker->postcode,
			'res_country_id' => 250,
			'pos_address_1' => $this->faker->address,
			'pos_address_2' => $this->faker->address,
			'pos_city' => $this->faker->city,
			'pos_province' => 'Eastern Cape',
			'pos_postal_code' => $this->faker->postcode,
			'pos_country_id' => 250,
		];
	}
}
